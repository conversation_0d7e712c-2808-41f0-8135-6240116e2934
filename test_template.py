#!/usr/bin/env python3
"""
测试简历模板渲染
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.resume_renderer import ResumeRenderer
from app.schemas.resume import ResumeData
from sample_data import resume_data

def test_template():
    """测试模板渲染"""
    print("开始测试模板渲染...")
    
    # 创建渲染器
    renderer = ResumeRenderer()
    
    # 创建简历数据对象
    resume_obj = ResumeData(**resume_data)
    
    # 渲染模板
    config = {
        "themeColor": "#44546b",
        "fontSize": 11,
        "spacing": 1.2
    }
    
    html_content = renderer.render_template(
        resume_data=resume_obj,
        template_id_from_client="templateA01.html",
        config_from_client=config
    )
    
    # 保存到文件
    output_file = "output/test_resume_templateA01.html"
    os.makedirs("output", exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"模板渲染完成，已保存到: {output_file}")
    print(f"HTML内容长度: {len(html_content)}")
    
    # 检查基本信息是否包含在HTML中
    if "基本信息" in html_content:
        print("✓ 基本信息模块已渲染")
    else:
        print("✗ 基本信息模块未渲染")
    
    if "求职意向" in html_content:
        print("✓ 求职意向模块已渲染")
    else:
        print("✗ 求职意向模块未渲染")
        
    if "李华" in html_content:
        print("✓ 姓名数据已渲染")
    else:
        print("✗ 姓名数据未渲染")
        
    if "前端开发工程师" in html_content:
        print("✓ 工作职位数据已渲染")
    else:
        print("✗ 工作职位数据未渲染")

if __name__ == "__main__":
    test_template()
