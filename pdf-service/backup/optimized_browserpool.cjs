const puppeteer = require('puppeteer-core');
const { v4: uuidv4 } = require('uuid');
const { Mutex } = require('async-mutex');
const fs = require('fs');

// 获取Chrome可执行文件路径
const chromePath = '/home/<USER>/.cache/puppeteer/chrome-headless-shell/linux-136.0.7103.92/chrome-headless-shell-linux64/chrome-headless-shell';

/**
 * 浏览器实例类
 */
class Browser {
  constructor(id, browserOptions) {
    this.id = id;
    this.browser = null;
    this.pages = new Map(); // [pageId] => { page, createdAt }
    this.usingPages = 0;    // 当前正在使用的页面数
    this.totalPages = 0;    // 总创建页面数
    this.usageCount = 0;    // 使用次数
    this.status = 'initializing'; // initializing | ready | restarting | error | closed
    this.browserOptions = browserOptions;
    this.creationTime = Date.now();
    this.lastUsedTime = Date.now();
    this.mutex = new Mutex(); // 添加互斥锁，确保并发操作安全
    this.restartTimeoutId = null;
    this.timeToRestartBrowser = 5000; // 重启等待时间，单位毫秒
    this.timeoutDefaultNavigation = 30000;
    this.timeoutDefault = 30000;
    this.errorCount = 0; // 错误计数
  }

  /**
   * 初始化浏览器实例
   */
  async initialize() {
    try {
      const startTime = Date.now();
      this.browser = await puppeteer.launch(this.browserOptions);

      // 监听浏览器断开连接事件
      this.browser.on('disconnected', () => {
        console.log(`Browser ${this.id} disconnected`);
        this.status = 'error';
      });

      this.status = 'ready';
      this.creationTime = Date.now();
      this.lastUsedTime = Date.now();
      this.errorCount = 0;
      console.log(`Browser ${this.id} initialize time: ${Date.now() - startTime}ms`);
      return this;
    } catch (error) {
      this.status = 'error';
      this.errorCount++;
      console.error(`Browser ${this.id} initialization error:`, error);
      throw error;
    }
  }

  /**
   * 获取新页面
   */
  async getPage() {
    const release = await this.mutex.acquire();
    try {
      if (this.status !== 'ready') {
        throw new Error(`Browser ${this.id} is not ready, current status: ${this.status}`);
      }

      const pageId = uuidv4();
      const page = await this.browser.newPage();

      // 设置页面超时
      page.setDefaultNavigationTimeout(this.timeoutDefaultNavigation);
      page.setDefaultTimeout(this.timeoutDefault);

      // 监听页面错误
      page.on('error', (error) => {
        console.error(`Page ${pageId} error:`, error);
        this.errorCount++;
      });

      // 存储页面信息
      this.pages.set(pageId, {
        page,
        createdAt: Date.now(),
        errors: 0
      });

      this.usingPages++;
      this.totalPages++;
      this.usageCount++;
      this.lastUsedTime = Date.now();

      return { page, pageId, browserId: this.id };
    } catch (error) {
      console.error(`Error getting page from browser ${this.id}:`, error);
      this.errorCount++;
      throw error;
    } finally {
      release();
    }
  }

  /**
   * 释放页面
   */
  async releasePage(pageId) {
    const release = await this.mutex.acquire();
    try {
      const pageInfo = this.pages.get(pageId);
      if (!pageInfo) return;

      try {
        await pageInfo.page.close();
      } catch (e) {
        console.error(`Error closing page ${pageId}:`, e);
        this.errorCount++;
      }

      this.pages.delete(pageId);
      this.usingPages--;
      this.lastUsedTime = Date.now();
    } catch (error) {
      console.error(`Error releasing page ${pageId}:`, error);
      this.errorCount++;
    } finally {
      release();
    }
  }

  /**
   * 关闭浏览器
   */
  async close() {
    const release = await this.mutex.acquire();
    try {
      // 关闭所有页面
      for (const [pageId, pageInfo] of this.pages) {
        try {
          await pageInfo.page.close();
        } catch (e) {
          console.error(`Error closing page ${pageId} during browser close:`, e);
        }
      }

      // 清空页面集合
      this.pages.clear();
      this.usingPages = 0;

      // 关闭浏览器
      if (this.browser) {
        await this.browser.close();
      }

      this.status = 'closed';
      console.log(`Browser ${this.id} closed successfully`);
    } catch (error) {
      console.error(`Error closing browser ${this.id}:`, error);
      this.status = 'error';
      this.errorCount++;
    } finally {
      release();
    }
  }

  /**
   * 检查浏览器健康状态
   */
  async checkHealth() {
    try {
      if (!this.browser || this.status === 'closed' || this.status === 'error') {
        return {
          isAlive: false,
          creationTime: this.creationTime,
          lastUsedTime: this.lastUsedTime,
          totalPages: this.totalPages,
          usingPages: this.usingPages,
          usageCount: this.usageCount,
          errorCount: this.errorCount,
          status: this.status
        };
      }

      // 检查浏览器是否仍然连接
      const isConnected = this.browser.isConnected();

      // 尝试获取版本信息作为健康检查
      let version;
      try {
        version = await this.browser.version();
      } catch (e) {
        console.error(`Error getting browser version for ${this.id}:`, e);
        this.errorCount++;
        return {
          isAlive: false,
          creationTime: this.creationTime,
          lastUsedTime: this.lastUsedTime,
          totalPages: this.totalPages,
          usingPages: this.usingPages,
          usageCount: this.usageCount,
          errorCount: this.errorCount,
          status: this.status
        };
      }

      return {
        isAlive: isConnected && version !== undefined,
        creationTime: this.creationTime,
        lastUsedTime: this.lastUsedTime,
        totalPages: this.totalPages,
        usingPages: this.usingPages,
        usageCount: this.usageCount,
        errorCount: this.errorCount,
        status: this.status
      };
    } catch (error) {
      console.error(`Error checking health for browser ${this.id}:`, error);
      this.errorCount++;
      return {
        isAlive: false,
        creationTime: this.creationTime,
        lastUsedTime: this.lastUsedTime,
        totalPages: this.totalPages,
        usingPages: this.usingPages,
        usageCount: this.usageCount,
        errorCount: this.errorCount,
        status: this.status
      };
    }
  }

  /**
   * 重启浏览器
   */
  async restart() {
    if (this.restartTimeoutId) clearTimeout(this.restartTimeoutId);
    this.status = 'restarting';

    console.log(`Browser ${this.id} scheduled for restart in ${this.timeToRestartBrowser}ms`);

    this.restartTimeoutId = setTimeout(async () => {
      const release = await this.mutex.acquire();
      try {
        // 确保所有页面已关闭
        for (const [pageId, pageInfo] of this.pages) {
          try {
            await pageInfo.page.close();
          } catch (e) {
            console.error(`Error closing page ${pageId} during restart:`, e);
          }
        }

        this.pages.clear();
        this.usingPages = 0;

        // 关闭旧浏览器
        if (this.browser) {
          await this.browser.close();
        }

        // 初始化新浏览器
        await this.initialize();
        console.log(`Browser ${this.id} restarted successfully`);
      } catch (error) {
        console.error(`Error restarting browser ${this.id}:`, error);
        this.status = 'error';
        this.errorCount++;
      } finally {
        release();
      }
    }, this.timeToRestartBrowser);
  }
}

/**
 * 浏览器池类
 */
class BrowserPool {
  constructor(options) {
    // 默认配置与用户配置合并
    this.config = {
      initialSize: 2,                        // 初始浏览器数量
      maxBrowsers: 4,                        // 最大浏览器实例数
      minBrowsers: 1,                        // 最小浏览器实例数
      maxPagesPerBrowser: 5,                 // 每个浏览器最多创建的页面数
      maxTotalUsage: 100,                    // 单个浏览器最大使用次数
      maxAvgUsage: 10,                       // 平均使用阈值，超过此值创建新实例
      minAvgUsage: 3,                        // 最小平均使用阈值，低于此值关闭多余实例
      maxErrorCount: 5,                      // 最大错误次数，超过此值重启浏览器
      maxIdleTime: 5 * 60 * 1000,            // 最大空闲时间，单位毫秒（5分钟）
      maxLifeTime: 60 * 60 * 1000,           // 最大生命周期，单位毫秒（1小时）
      intervalHealthCheck: 15000,            // 健康检查间隔，单位毫秒
      intervalScaleCheck: 30000,             // 扩缩容检查间隔，单位毫秒
      ...options
    };

    this.browsers = new Map();               // 存储所有浏览器实例
    this.mutex = new Mutex();                // 互斥锁，确保并发操作安全
    this.healthCheckIntervalId = null;       // 健康检查定时器ID
    this.scaleCheckIntervalId = null;        // 扩缩容检查定时器ID
    this.isShuttingDown = false;             // 是否正在关闭
  }

  /**
   * 初始化浏览器池
   */
  async initializePool() {
    const release = await this.mutex.acquire();
    try {
      // 创建初始浏览器实例
      await this.createBrowsers(this.config.initialSize);

      // 启动健康检查
      this.startHealthCheck();

      // 启动扩缩容检查
      this.startScaleCheck();

      console.log(`Browser pool initialized with ${this.browsers.size} browsers`);
    } catch (error) {
      console.error('Error initializing browser pool:', error);
    } finally {
      release();
    }
  }

  /**
   * 创建多个浏览器实例
   */
  async createBrowsers(count) {
    const promises = [];
    for (let i = 0; i < count; i++) {
      promises.push(this.createBrowser());
    }
    await Promise.allSettled(promises);
  }

  /**
   * 创建单个浏览器实例
   */
  async createBrowser() {
    const release = await this.mutex.acquire();
    try {
      // 检查是否达到最大浏览器数量
      if (this.browsers.size >= this.config.maxBrowsers) {
        console.log(`Cannot create more browsers, reached max limit (${this.config.maxBrowsers})`);
        return null;
      }

      const id = uuidv4();
      const browser = new Browser(id, this.config.browserOptions);

      try {
        await browser.initialize();
        this.browsers.set(id, browser);
        console.log(`Created new browser ${id}, total browsers: ${this.browsers.size}`);
        return id;
      } catch (error) {
        console.error(`Failed to initialize browser ${id}:`, error);
        return null;
      }
    } finally {
      release();
    }
  }

  /**
   * 重启浏览器
   */
  async restartBrowser(browserId) {
    const release = await this.mutex.acquire();
    try {
      const browser = this.browsers.get(browserId);
      if (!browser) return;

      console.log(`Restarting browser ${browserId}`);

      try {
        await browser.restart();
      } catch (error) {
        console.error(`Restart failed for browser ${browserId}:`, error);

        // 如果重启失败，删除此浏览器并创建新的
        this.browsers.delete(browserId);
        await this.createBrowser();
      }
    } finally {
      release();
    }
  }

  /**
   * 关闭浏览器
   */
  async closeBrowser(browserId) {
    const release = await this.mutex.acquire();
    try {
      const browser = this.browsers.get(browserId);
      if (!browser) return;

      // 只关闭没有活跃页面的浏览器
      if (browser.usingPages > 0) {
        console.log(`Cannot close browser ${browserId}, it has ${browser.usingPages} active pages`);
        return;
      }

      console.log(`Closing browser ${browserId}`);

      try {
        await browser.close();
        this.browsers.delete(browserId);
        console.log(`Browser ${browserId} closed, total browsers: ${this.browsers.size}`);
      } catch (error) {
        console.error(`Failed to close browser ${browserId}:`, error);
      }
    } finally {
      release();
    }
  }

  /**
   * 选择最适合的浏览器
   */
  selectBrowser(candidates) {
    // 按照使用页面数量、总使用次数和错误次数进行加权选择
    const weights = candidates.map(browser => {
      // 基础权重：页面数量越少越好
      let weight = Math.max(0, this.config.maxPagesPerBrowser - browser.usingPages);

      // 使用次数接近上限的减少权重
      const usageRatio = browser.usageCount / this.config.maxTotalUsage;
      if (usageRatio > 0.8) {
        weight *= (1 - usageRatio);
      }

      // 错误次数多的减少权重
      if (browser.errorCount > 0) {
        weight *= (1 - browser.errorCount / this.config.maxErrorCount);
      }

      return { browser, weight: Math.max(0.1, weight) };
    });

    // 计算总权重
    const totalWeight = weights.reduce((sum, item) => sum + item.weight, 0);

    // 随机选择
    let random = Math.random() * totalWeight;
    for (const item of weights) {
      random -= item.weight;
      if (random <= 0) return item.browser;
    }

    // 默认返回第一个
    return candidates[0];
  }

  /**
   * 获取页面
   */
  async getPage() {
    const release = await this.mutex.acquire();
    try {
      if (this.isShuttingDown) {
        throw new Error('Browser pool is shutting down');
      }

      // 筛选可用的浏览器
      const candidates = Array.from(this.browsers.values())
        .filter(browser => {
          // 状态必须为ready
          if (browser.status !== 'ready') return false;

          // 页面数量不能超过上限
          if (browser.usingPages >= this.config.maxPagesPerBrowser) return false;

          // 使用次数不能超过上限
          if (browser.usageCount >= this.config.maxTotalUsage) return false;

          // 错误次数不能超过上限
          if (browser.errorCount >= this.config.maxErrorCount) return false;

          return true;
        });

      if (candidates.length > 0) {
        // 选择最适合的浏览器
        const browser = this.selectBrowser(candidates);
        return await browser.getPage();
      } else {
        // 没有可用浏览器，创建新的
        console.log('Browser pool getPage: 没有可用浏览器，创建新浏览器');
        const newBrowserId = await this.createBrowser();

        if (!newBrowserId) {
          throw new Error('Failed to create new browser');
        }

        const newBrowser = this.browsers.get(newBrowserId);
        return await newBrowser.getPage();
      }
    } catch (error) {
      console.error('Browser pool getPage error:', error);
      throw error;
    } finally {
      release();
    }
  }

  /**
   * 释放页面
   */
  async releasePage({ pageId, browserId }) {
    try {
      const browser = this.browsers.get(browserId);
      if (!browser) {
        console.warn(`Browser ${browserId} not found when releasing page ${pageId}`);
        return;
      }

      await browser.releasePage(pageId);
    } catch (error) {
      console.error(`Error releasing page ${pageId} from browser ${browserId}:`, error);
    }
  }

  /**
   * 计算平均使用量
   */
  calculateAvgUsage() {
    if (this.browsers.size === 0) return 0;

    const total = Array.from(this.browsers.values())
      .reduce((sum, browser) => sum + browser.usingPages, 0);

    return total / this.browsers.size;
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    if (this.healthCheckIntervalId) {
      clearInterval(this.healthCheckIntervalId);
    }

    this.healthCheckIntervalId = setInterval(async () => {
      if (this.isShuttingDown) return;

      const release = await this.mutex.acquire();
      try {
        console.log(`Running health check for ${this.browsers.size} browsers`);

        const now = Date.now();
        const browserEntries = Array.from(this.browsers.entries());

        for (const [id, browser] of browserEntries) {
          try {
            const health = await browser.checkHealth();

            // 检查是否需要重启浏览器
            const needsRestart = (
              // 浏览器不可用
              !health.isAlive ||
              // 使用次数超过上限
              browser.usageCount >= this.config.maxTotalUsage ||
              // 错误次数超过上限
              browser.errorCount >= this.config.maxErrorCount ||
              // 生命周期超过上限
              (now - browser.creationTime) > this.config.maxLifeTime ||
              // 页面数量超过上限
              browser.totalPages >= this.config.maxPagesPerBrowser
            );

            if (needsRestart && browser.usingPages === 0) {
              console.log(`Browser ${id} needs restart: isAlive=${health.isAlive}, usageCount=${browser.usageCount}, errorCount=${browser.errorCount}, age=${(now - browser.creationTime)/1000}s, totalPages=${browser.totalPages}`);
              await this.restartBrowser(id);
            }
          } catch (error) {
            console.error(`Error during health check for browser ${id}:`, error);
          }
        }
      } catch (error) {
        console.error('Error during health check:', error);
      } finally {
        release();
      }
    }, this.config.intervalHealthCheck);
  }

  /**
   * 启动扩缩容检查
   */
  startScaleCheck() {
    if (this.scaleCheckIntervalId) {
      clearInterval(this.scaleCheckIntervalId);
    }

    this.scaleCheckIntervalId = setInterval(async () => {
      if (this.isShuttingDown) return;

      const release = await this.mutex.acquire();
      try {
        // 计算平均使用量
        const avgUsage = this.calculateAvgUsage();
        console.log(`目前浏览器 ${this.browsers.size} 个，平均使用 ${avgUsage.toFixed(2)} 个页面`);

        // 扩容：如果平均使用量超过阈值，创建新浏览器
        if (avgUsage > this.config.maxAvgUsage &&
            this.browsers.size < this.config.maxBrowsers) {
          console.log(`Average usage (${avgUsage.toFixed(2)}) exceeds threshold (${this.config.maxAvgUsage}), creating new browser`);
          await this.createBrowser();
        }

        // 缩容：如果平均使用量低于阈值，关闭多余浏览器
        if (avgUsage < this.config.minAvgUsage &&
            this.browsers.size > this.config.minBrowsers) {
          console.log(`Average usage (${avgUsage.toFixed(2)}) below threshold (${this.config.minAvgUsage}), considering closing browsers`);

          // 找出空闲时间最长的浏览器
          const idleBrowsers = Array.from(this.browsers.values())
            .filter(browser => browser.usingPages === 0 && browser.status === 'ready')
            .sort((a, b) => a.lastUsedTime - b.lastUsedTime);

          // 关闭最早空闲的浏览器，但保留最小数量
          if (idleBrowsers.length > 0 && this.browsers.size > this.config.minBrowsers) {
            const oldestBrowser = idleBrowsers[0];
            const idleTime = Date.now() - oldestBrowser.lastUsedTime;

            // 只关闭空闲时间超过阈值的浏览器
            if (idleTime > this.config.maxIdleTime) {
              console.log(`Closing idle browser ${oldestBrowser.id}, idle for ${idleTime/1000}s`);
              await this.closeBrowser(oldestBrowser.id);
            }
          }
        }
      } catch (error) {
        console.error('Error during scale check:', error);
      } finally {
        release();
      }
    }, this.config.intervalScaleCheck);
  }

  /**
   * 关闭所有浏览器
   */
  async closeAllBrowsers() {
    const release = await this.mutex.acquire();
    try {
      this.isShuttingDown = true;

      // 停止所有定时器
      if (this.healthCheckIntervalId) {
        clearInterval(this.healthCheckIntervalId);
        this.healthCheckIntervalId = null;
      }

      if (this.scaleCheckIntervalId) {
        clearInterval(this.scaleCheckIntervalId);
        this.scaleCheckIntervalId = null;
      }

      console.log(`Closing all browsers (${this.browsers.size})`);

      // 关闭所有浏览器
      const promises = [];
      for (const [id, browser] of this.browsers) {
        promises.push(
          browser.close()
            .then(() => console.log(`Browser ${id} closed successfully`))
            .catch(error => console.error(`Error closing browser ${id}:`, error))
        );
      }

      await Promise.allSettled(promises);
      this.browsers.clear();

      console.log('All browsers closed');
    } catch (error) {
      console.error('Error closing all browsers:', error);
    } finally {
      release();
    }
  }
}

// 导出模块
module.exports = {
  Browser,
  BrowserPool,
  chromePath
};