#!/usr/bin/env python3
import requests

# 简单的测试数据
data = {
    "template_id": "templateA01_debug",
    "resume_data": {
        "moduleOrders": {
            "basicInfo": 1,
            "jobIntention": 2,
            "education": 3,
            "skills": 4
        },
        "basicInfo": {
            "name": "张三",
            "phone": "13800138000",
            "email": "<EMAIL>",
            "gender": "男",
            "age": "25"
        },
        "jobIntention": {
            "position": "前端开发工程师",
            "salary": "10k-15k",
            "city": "北京"
        },
        "education": [
            {
                "school": "北京大学",
                "major": "计算机科学",
                "degree": "本科",
                "startDate": "2016-09",
                "endDate": "2020-06"
            }
        ],
        "skills": [
            "HTML/CSS/JavaScript",
            "Vue.js/React",
            "Node.js"
        ]
    },
    "theme_config": {
        "themeColor": "#4A90E2"
    }
}

try:
    response = requests.post("http://localhost:18080/resume/preview", json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        html = result.get("html", "")
        print(f"HTML长度: {len(html)}")

        # 保存HTML文件
        with open("debug_templateA01.html", "w", encoding="utf-8") as f:
            f.write(html)
        print("已保存到 debug_templateA01.html")
    else:
        print(f"错误: {response.text}")
except Exception as e:
    print(f"请求失败: {e}")
