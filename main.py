import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
from dotenv import load_dotenv

from app.routers import resume, auth, user_action, feedback
from app.config import settings, validate_config
from env import RESUME_SERVER_PORT

# 加载环境变量
load_dotenv()

# 验证配置
validate_config()

app = FastAPI(
    title="简历服务API",
    description="微信小程序简历服务后端API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建静态文件目录（如果不存在）
static_dir = os.path.join(os.path.dirname(__file__), "static")
if not os.path.exists(static_dir):
    os.makedirs(static_dir)

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# 添加路由
# app.include_router(api.router)
app.include_router(resume.router)
app.include_router(auth.router)
app.include_router(user_action.router)
app.include_router(feedback.router)

@app.get("/")
async def root():
    return {"message": "欢迎使用FastAPI服务"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=RESUME_SERVER_PORT, reload=True)