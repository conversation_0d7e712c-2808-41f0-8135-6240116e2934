"""
数据库迁移脚本：添加用户会员字段
"""
import mysql.connector
from mysql.connector import Error
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def add_member_field():
    """添加is_member字段到users表"""
    connection = None
    cursor = None
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logger.info("连接数据库成功")
        
        # 检查字段是否已存在
        check_sql = """
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'resume_service' 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'is_member'
        """
        
        cursor.execute(check_sql)
        field_exists = cursor.fetchone()[0] > 0
        
        if field_exists:
            logger.info("is_member字段已存在，跳过添加")
            return True
        
        # 添加is_member字段
        alter_sql = """
        ALTER TABLE users 
        ADD COLUMN is_member BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否会员'
        """
        
        cursor.execute(alter_sql)
        connection.commit()
        
        logger.info("成功添加is_member字段到users表")
        
        # 验证字段添加成功
        cursor.execute(check_sql)
        field_exists = cursor.fetchone()[0] > 0
        
        if field_exists:
            logger.info("字段添加验证成功")
            return True
        else:
            logger.error("字段添加验证失败")
            return False
            
    except Error as e:
        logger.error(f"数据库操作错误: {e}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()
            logger.info("数据库连接已关闭")

def show_table_structure():
    """显示users表结构"""
    connection = None
    cursor = None
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        cursor.execute("DESCRIBE users")
        columns = cursor.fetchall()
        
        logger.info("users表结构:")
        for column in columns:
            logger.info(f"  {column[0]} - {column[1]} - {column[2]} - {column[3]} - {column[4]} - {column[5]}")
            
    except Error as e:
        logger.error(f"查询表结构错误: {e}")
        
    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()

def main():
    """主函数"""
    logger.info("开始数据库迁移：添加用户会员字段")
    
    # 显示迁移前的表结构
    logger.info("=== 迁移前表结构 ===")
    show_table_structure()
    
    # 执行迁移
    success = add_member_field()
    
    if success:
        logger.info("=== 迁移后表结构 ===")
        show_table_structure()
        logger.info("数据库迁移完成！")
    else:
        logger.error("数据库迁移失败！")

if __name__ == "__main__":
    main()
