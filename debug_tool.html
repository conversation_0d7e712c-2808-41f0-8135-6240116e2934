<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A01模板调试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background-color: #34495e;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .preview-area {
            flex: 1;
            background-color: white;
            overflow: auto;
            position: relative;
        }

        .debug-panel h2 {
            color: #3498db;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .debug-section {
            margin-bottom: 25px;
            padding: 15px;
            background-color: #34495e;
            border-radius: 8px;
        }

        .debug-section h3 {
            color: #e74c3c;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .color-legend {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
        }

        .color-box {
            width: 20px;
            height: 15px;
            border-radius: 3px;
            border: 1px solid #fff;
        }

        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .btn-danger {
            background-color: #e74c3c;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .btn-success {
            background-color: #27ae60;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #bdc3c7;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #7f8c8d;
            border-radius: 4px;
            background-color: #2c3e50;
            color: white;
            font-size: 12px;
        }

        .tips {
            background-color: #f39c12;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            margin-bottom: 15px;
        }

        .element-info {
            position: fixed;
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            z-index: 10000;
            display: none;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 18px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="debug-panel">
            <h2>🔧 A01模板调试工具</h2>
            
            <div class="tips">
                💡 这个工具可以帮助你识别和调整A01模板中的各个元素
            </div>

            <div class="debug-section">
                <h3>🎨 元素颜色标识</h3>
                <div class="color-legend">
                    <div class="legend-item">
                        <div class="color-box" style="background-color: #FF0000;"></div>
                        <span>整个简历容器</span>
                    </div>
                    <div class="legend-item">
                        <div class="color-box" style="background-color: #00FF00;"></div>
                        <span>顶部装饰条</span>
                    </div>
                    <div class="legend-item">
                        <div class="color-box" style="background-color: rgba(255, 255, 0, 0.3);"></div>
                        <span>头部区域</span>
                    </div>
                    <div class="legend-item">
                        <div class="color-box" style="background-color: rgba(255, 0, 255, 0.2);"></div>
                        <span>标题文字</span>
                    </div>
                    <div class="legend-item">
                        <div class="color-box" style="background-color: rgba(0, 255, 255, 0.2);"></div>
                        <span>图标组</span>
                    </div>
                    <div class="legend-item">
                        <div class="color-box" style="background-color: rgba(173, 216, 230, 0.5);"></div>
                        <span>基本信息区域</span>
                    </div>
                    <div class="legend-item">
                        <div class="color-box" style="background-color: rgba(128, 128, 128, 0.1);"></div>
                        <span>各个section</span>
                    </div>
                    <div class="legend-item">
                        <div class="color-box" style="background-color: rgba(255, 192, 203, 0.3);"></div>
                        <span>标题包装器</span>
                    </div>
                </div>
            </div>

            <div class="debug-section">
                <h3>⚙️ 调试操作</h3>
                <button class="btn" onclick="loadDebugTemplate()">🔄 重新加载调试模板</button>
                <button class="btn btn-success" onclick="generateNormalTemplate()">✨ 生成正常版本</button>
                <button class="btn btn-danger" onclick="toggleDebugMode()">🎯 切换调试模式</button>
            </div>

            <div class="debug-section">
                <h3>🎨 主题配置</h3>
                <div class="input-group">
                    <label>主题颜色:</label>
                    <input type="color" id="themeColor" value="#4A90E2" onchange="updateTheme()">
                </div>
                <div class="input-group">
                    <label>字体大小:</label>
                    <input type="range" id="fontSize" min="10" max="16" value="12" onchange="updateTheme()">
                    <span id="fontSizeValue">12pt</span>
                </div>
                <div class="input-group">
                    <label>行间距:</label>
                    <input type="range" id="spacing" min="1.0" max="2.0" step="0.1" value="1.4" onchange="updateTheme()">
                    <span id="spacingValue">1.4</span>
                </div>
            </div>

            <div class="debug-section">
                <h3>📝 调试说明</h3>
                <div style="font-size: 11px; line-height: 1.4; color: #bdc3c7;">
                    <p><strong>使用方法：</strong></p>
                    <p>1. 观察右侧预览中的彩色边框</p>
                    <p>2. 根据颜色标识找到要调整的元素</p>
                    <p>3. 在templateA01.html中修改对应CSS</p>
                    <p>4. 重新加载查看效果</p>
                    <br>
                    <p><strong>常用调整：</strong></p>
                    <p>• margin: 外边距</p>
                    <p>• padding: 内边距</p>
                    <p>• position: 位置</p>
                    <p>• width/height: 尺寸</p>
                    <p>• top/left/right/bottom: 偏移</p>
                </div>
            </div>

            <div class="debug-section">
                <h3>🔗 快速链接</h3>
                <button class="btn" onclick="openTemplateFile()">📝 编辑模板文件</button>
                <button class="btn" onclick="openCSSReference()">📚 CSS参考</button>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="toolbar">
            <span>🎯 A01模板调试预览</span>
            <button class="btn" onclick="refreshPreview()">🔄 刷新</button>
            <button class="btn" onclick="exportDebugHTML()">💾 导出HTML</button>
            <span id="status">就绪</span>
        </div>
        <div class="preview-area">
            <div class="loading" id="loading">正在加载调试模板...</div>
            <iframe id="previewFrame" style="display: none;"></iframe>
        </div>
    </div>

    <div class="element-info" id="elementInfo"></div>

    <script>
        let debugMode = true;
        let currentTheme = {
            themeColor: '#4A90E2',
            fontSize: 12,
            spacing: 1.4
        };

        // 加载调试模板
        function loadDebugTemplate() {
            document.getElementById('status').textContent = '正在加载...';
            document.getElementById('loading').style.display = 'flex';
            document.getElementById('previewFrame').style.display = 'none';
            
            // 模拟加载调试模板
            setTimeout(() => {
                const iframe = document.getElementById('previewFrame');
                iframe.src = 'debug_templateA01.html';
                iframe.onload = () => {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('previewFrame').style.display = 'block';
                    document.getElementById('status').textContent = '调试模板已加载';
                };
            }, 500);
        }

        // 生成正常版本
        function generateNormalTemplate() {
            document.getElementById('status').textContent = '正在生成正常版本...';
            // 这里可以调用API生成正常版本的模板
            setTimeout(() => {
                document.getElementById('status').textContent = '正常版本已生成';
                alert('正常版本已生成！请检查output目录');
            }, 1000);
        }

        // 切换调试模式
        function toggleDebugMode() {
            debugMode = !debugMode;
            document.getElementById('status').textContent = debugMode ? '调试模式已开启' : '调试模式已关闭';
        }

        // 更新主题
        function updateTheme() {
            const themeColor = document.getElementById('themeColor').value;
            const fontSize = document.getElementById('fontSize').value;
            const spacing = document.getElementById('spacing').value;
            
            document.getElementById('fontSizeValue').textContent = fontSize + 'pt';
            document.getElementById('spacingValue').textContent = spacing;
            
            currentTheme = { themeColor, fontSize, spacing };
            document.getElementById('status').textContent = '主题已更新';
        }

        // 刷新预览
        function refreshPreview() {
            loadDebugTemplate();
        }

        // 导出HTML
        function exportDebugHTML() {
            const link = document.createElement('a');
            link.href = 'debug_templateA01.html';
            link.download = 'debug_templateA01.html';
            link.click();
        }

        // 打开模板文件
        function openTemplateFile() {
            alert('请在代码编辑器中打开: app/templates/templateA01.html');
        }

        // 打开CSS参考
        function openCSSReference() {
            window.open('https://developer.mozilla.org/zh-CN/docs/Web/CSS', '_blank');
        }

        // 页面加载完成后自动加载调试模板
        window.onload = function() {
            loadDebugTemplate();
        };
    </script>
</body>
</html>
