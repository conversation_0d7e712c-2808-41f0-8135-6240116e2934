"""
数据库初始化脚本
创建所有数据表
"""
from app.database import engine, Base
from app.models import User, UserAction, Feedback, FeedbackReply

def create_tables():
    """创建所有数据表"""
    print("正在创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成！")

def drop_tables():
    """删除所有数据表（谨慎使用）"""
    print("正在删除数据库表...")
    Base.metadata.drop_all(bind=engine)
    print("数据库表删除完成！")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "drop":
        drop_tables()
    
    create_tables()
    
    print("\n数据库初始化完成！")
    print("数据库连接信息：")
    print(f"引擎: {engine.url}")
    print("\n创建的表：")
    print("- users: 用户表")
    print("- user_actions: 用户行为记录表") 
    print("- feedback: 反馈表")
    print("- feedback_replies: 反馈回复表")
