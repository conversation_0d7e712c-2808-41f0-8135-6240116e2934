# 微信小程序简历服务后端 API 接口文档

## 基础信息

**服务地址**: `http://localhost:18080`
**API版本**: v1.0.0
**认证方式**: <PERSON>er <PERSON> (JWT)

## 通用说明

### 认证方式
除了登录接口外，其他接口都需要在请求头中携带认证token：
```
Authorization: Bearer <access_token>
```

### 通用响应格式
```json
{
  "message": "操作成功",
  "data": {}
}
```

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

### 状态码说明
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未认证或token无效
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 参数验证失败
- `500`: 服务器内部错误

---

## 1. 用户认证模块 (/auth)

### 1.1 微信登录
**接口**: `POST /auth/login`
**描述**: 微信小程序用户登录，获取访问token

**请求参数**:
```json
{
  "code": "微信登录code",
  "user_info": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "gender": 1,
    "country": "中国",
    "province": "北京",
    "city": "北京"
  }
}
```

**参数说明**:
- `code` (必填): 微信小程序wx.login()获取的code
- `user_info` (可选): 用户基本信息
  - `gender`: 0-未知, 1-男, 2-女

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_info": {
    "id": 1,
    "openid": "oxxxxxxxxxxxxxx",
    "nickname": "用户昵称",
    "avatar_url": "https://example.com/avatar.jpg",
    "gender": 1,
    "country": "中国",
    "province": "北京",
    "city": "北京",
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T00:00:00"
  }
}
```

### 1.2 获取用户信息
**接口**: `GET /auth/user`
**描述**: 获取当前登录用户的详细信息
**认证**: 需要

**响应示例**:
```json
{
  "id": 1,
  "openid": "oxxxxxxxxxxxxxx",
  "nickname": "用户昵称",
  "avatar_url": "https://example.com/avatar.jpg",
  "gender": 1,
  "country": "中国",
  "province": "北京",
  "city": "北京",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### 1.3 更新用户信息
**接口**: `PUT /auth/user`
**描述**: 更新当前用户的基本信息
**认证**: 需要

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar_url": "新头像URL",
  "gender": 2,
  "country": "中国",
  "province": "上海",
  "city": "上海"
}
```

**响应示例**:
```json
{
  "id": 1,
  "openid": "oxxxxxxxxxxxxxx",
  "nickname": "新昵称",
  "avatar_url": "新头像URL",
  "gender": 2,
  "country": "中国",
  "province": "上海",
  "city": "上海",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T12:00:00"
}
```

### 1.4 刷新访问令牌
**接口**: `POST /auth/refresh`
**描述**: 刷新当前用户的访问令牌
**认证**: 需要

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_info": {
    "id": 1,
    "openid": "oxxxxxxxxxxxxxx",
    "nickname": "用户昵称",
    "avatar_url": "https://example.com/avatar.jpg",
    "gender": 1,
    "country": "中国",
    "province": "北京",
    "city": "北京",
    "is_member": false,
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T00:00:00"
  }
}
```

### 1.5 查询会员状态
**接口**: `GET /auth/member-status`
**描述**: 查询当前用户的会员状态
**认证**: 需要

**响应示例**:
```json
{
  "is_member": false,
  "openid": "oxxxxxxxxxxxxxx",
  "message": "普通用户"
}
```

**响应字段说明**:
- `is_member`: 是否会员，true-会员，false-非会员
- `openid`: 用户的微信openid
- `message`: 状态描述，"会员用户" 或 "普通用户"

---

## 2. 简历处理模块 (/resume)

### 2.1 导出PDF
**接口**: `POST /resume/export-pdf`
**描述**: 将简历数据导出为PDF文件
**认证**: 可选（会记录用户行为）

**请求参数**:
```json
{
  "resume_data": {
    "personal_info": {
      "name": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "address": "北京市朝阳区"
    },
    "work_experience": [
      {
        "company": "ABC公司",
        "position": "软件工程师",
        "start_date": "2020-01",
        "end_date": "2023-12",
        "description": "负责前端开发工作"
      }
    ],
    "education": [
      {
        "school": "北京大学",
        "major": "计算机科学",
        "degree": "本科",
        "start_date": "2016-09",
        "end_date": "2020-06"
      }
    ]
  },
  "template_id": "templateA02.html",
  "theme_config": {
    "theme_color": "#007bff",
    "fontSize": "14px"
  }
}
```

**响应**: 直接返回PDF文件流
**Content-Type**: `application/pdf`
**Content-Disposition**: `attachment; filename=resume.pdf`

### 2.2 导出JPEG
**接口**: `POST /resume/export-jpeg`
**描述**: 将简历数据导出为JPEG图片
**认证**: 可选（会记录用户行为）

**请求参数**: 同导出PDF接口

**响应**: 直接返回JPEG文件流
**Content-Type**: `image/jpeg`
**Content-Disposition**: `attachment; filename=resume.jpeg`

### 2.3 预览简历
**接口**: `POST /resume/preview`
**描述**: 预览简历渲染结果，返回HTML内容用于调试
**认证**: 可选（会记录用户行为）

**请求参数**:
```json
{
  "resume_data": {
    "basicInfo": {
      "name": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "gender": "男",
      "age": "25",
      "address": "北京市朝阳区"
    },
    "jobIntention": {
      "position": "软件工程师",
      "salary": "15-20K",
      "city": "北京",
      "jobType": "全职"
    },
    "education": [
      {
        "school": "北京大学",
        "major": "计算机科学与技术",
        "degree": "本科",
        "startDate": "2018-09",
        "endDate": "2022-06",
        "description": "主修计算机科学与技术，GPA 3.8/4.0"
      }
    ],
    "work": [
      {
        "company": "腾讯科技",
        "position": "前端开发工程师",
        "startDate": "2022-07",
        "endDate": "至今",
        "description": "负责微信小程序开发，参与多个项目的前端架构设计"
      }
    ],
    "project": [
      {
        "projectName": "简历生成系统",
        "role": "技术负责人",
        "startDate": "2023-01",
        "endDate": "2023-06",
        "description": "基于FastAPI和Vue.js开发的在线简历生成系统"
      }
    ],
    "skills": [
      "Python - 熟练使用Python进行后端开发",
      "JavaScript - 熟练使用JavaScript进行前端开发"
    ],
    "moduleOrders": {
      "education": 1,
      "work": 2,
      "project": 3,
      "skills": 4
    }
  },
  "template_id": "templateA02",
  "theme_config": {
    "themeColor": "#2E75B6",
    "fontSize": 12,
    "spacing": 1.2
  }
}
```

**响应示例**:
```json
{
  "message": "简历预览生成成功",
  "html": "<html>...</html>",
  "template_id": "templateA02",
  "theme_config": {
    "themeColor": "#2E75B6",
    "fontSize": 12,
    "spacing": 1.2
  },
  "resume_data": {
    "basicInfo": {...},
    "jobIntention": {...}
  }
}
```

### 2.4 获取模板列表
**接口**: `GET /resume/templates`
**描述**: 获取所有可用的简历模板列表

**响应示例**:
```json
{
  "message": "获取模板列表成功",
  "templates": [
    "templateA01.html",
    "templateA02.html",
    "templateA03.html",
    "templateA04.html"
  ]
}
```

---

## 3. 用户反馈模块 (/feedback)

### 3.1 提交反馈
**接口**: `POST /feedback`
**描述**: 用户提交反馈信息
**认证**: 需要

**请求参数**:
```json
{
  "content": "反馈内容，最多2000字符",
  "contact_info": "联系方式（可选）"
}
```

**响应示例**:
```json
{
  "message": "反馈提交成功，我们会尽快处理"
}
```

### 3.2 获取反馈列表
**接口**: `GET /feedback`
**描述**: 获取当前用户的反馈列表
**认证**: 需要

**查询参数**:
- `status_filter` (可选): 状态筛选，可选值: `pending`, `replied`, `closed`
- `limit` (可选): 返回数量限制，默认20，最大100
- `offset` (可选): 偏移量，默认0

**请求示例**:
```
GET /feedback?status_filter=pending&limit=10&offset=0
```

**响应示例**:
```json
{
  "total": 5,
  "items": [
    {
      "id": 1,
      "user_id": 1,
      "content": "反馈内容",
      "contact_info": "联系方式",
      "status": "replied",
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T12:00:00",
      "replies": [
        {
          "id": 1,
          "admin_name": "客服小王",
          "reply_content": "感谢您的反馈，我们会及时处理",
          "created_at": "2024-01-01T12:00:00"
        }
      ]
    }
  ]
}
```

### 3.3 获取反馈详情
**接口**: `GET /feedback/{feedback_id}`
**描述**: 获取指定反馈的详细信息
**认证**: 需要

**路径参数**:
- `feedback_id`: 反馈ID

**响应示例**:
```json
{
  "id": 1,
  "user_id": 1,
  "content": "反馈内容",
  "contact_info": "联系方式",
  "status": "replied",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T12:00:00",
  "replies": [
    {
      "id": 1,
      "admin_name": "客服小王",
      "reply_content": "感谢您的反馈，我们会及时处理",
      "created_at": "2024-01-01T12:00:00"
    }
  ]
}
```

### 3.4 管理员回复反馈
**接口**: `POST /feedback/{feedback_id}/reply`
**描述**: 管理员回复用户反馈（需要管理员权限）

**路径参数**:
- `feedback_id`: 反馈ID

**请求参数**:
```json
{
  "reply_content": "回复内容，最多2000字符",
  "admin_name": "管理员名称"
}
```

**响应示例**:
```json
{
  "message": "回复成功"
}
```

### 3.5 更新反馈状态
**接口**: `PUT /feedback/{feedback_id}/status`
**描述**: 更新反馈状态（需要管理员权限）

**路径参数**:
- `feedback_id`: 反馈ID

**请求参数**:
```json
{
  "status": "closed"
}
```

**状态值说明**:
- `pending`: 待处理
- `replied`: 已回复
- `closed`: 已关闭

**响应示例**:
```json
{
  "message": "状态更新成功"
}
```

---

## 4. 数据模型说明

### 4.1 用户信息模型
```json
{
  "id": 1,
  "openid": "微信openid",
  "nickname": "用户昵称",
  "avatar_url": "头像URL",
  "gender": 1,
  "country": "国家",
  "province": "省份",
  "city": "城市",
  "is_member": false,
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

**字段说明**:
- `is_member`: 是否会员，true-会员，false-非会员，默认为false

### 4.2 反馈信息模型
```json
{
  "id": 1,
  "user_id": 1,
  "content": "反馈内容",
  "contact_info": "联系方式",
  "status": "pending|replied|closed",
  "created_at": "创建时间",
  "updated_at": "更新时间",
  "replies": [
    {
      "id": 1,
      "admin_name": "管理员名称",
      "reply_content": "回复内容",
      "created_at": "回复时间"
    }
  ]
}
```

### 4.3 简历数据模型
简历数据结构请参考现有的ResumeData模型，包含个人信息、工作经历、教育背景等字段。

---

## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未认证或token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用（如PDF服务异常） |

---

## 6. 开发注意事项

1. **认证token**: 登录成功后需要保存access_token，后续请求都需要携带
2. **token过期**: token默认30分钟过期，可通过refresh接口刷新
3. **文件下载**: PDF和JPEG导出接口返回文件流，需要特殊处理
4. **用户行为**: 简历相关操作会自动记录用户行为，无需额外调用
5. **错误处理**: 请根据HTTP状态码和返回的detail信息进行错误处理

## 7. 微信小程序集成示例

```javascript
// 登录示例
wx.login({
  success: (res) => {
    if (res.code) {
      wx.request({
        url: 'https://your-domain.com/auth/login',
        method: 'POST',
        data: {
          code: res.code,
          user_info: {
            nickName: '用户昵称',
            avatarUrl: '头像URL'
          }
        },
        success: (loginRes) => {
          wx.setStorageSync('access_token', loginRes.data.access_token);
        }
      });
    }
  }
});

// 带认证的请求示例
wx.request({
  url: 'https://your-domain.com/feedback',
  method: 'POST',
  header: {
    'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
  },
  data: {
    content: '反馈内容'
  },
  success: (res) => {
    console.log('反馈提交成功');
  }
});

// 查询会员状态示例
wx.request({
  url: 'https://your-domain.com/auth/member-status',
  method: 'GET',
  header: {
    'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
  },
  success: (res) => {
    if (res.statusCode === 200) {
      const isMember = res.data.is_member;
      console.log('用户会员状态:', isMember ? '会员' : '非会员');

      // 根据会员状态显示不同的UI或功能
      if (isMember) {
        // 显示会员专属功能
        console.log('显示会员专属功能');
      } else {
        // 显示升级会员提示
        console.log('显示升级会员提示');
      }
    }
  },
  fail: (err) => {
    console.error('查询会员状态失败:', err);
  }
});
```
