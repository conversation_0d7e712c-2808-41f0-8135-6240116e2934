<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>张三的简历 - 调试版</title>
  <link rel="stylesheet" href="http://localhost:18080/static/fontawesome/css/all.min.css">
  <style>
    :root {
      --theme-color: #4A90E2;
      --theme-color-light: #E8F4FD;
      --base-font-size: 11pt;
      --max-font-size: 12pt;
      --spacing: 1.2;
      --text-color: #333333;
      --secondary-color: #666666;
      --light-gray: #F5F5F5;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 10px 0;
      overflow-x: hidden;
    }

    /* 调试模式：为不同元素添加不同背景色 */
    .debug-mode {
      border: 2px solid red !important;
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      padding: 0;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      position: relative;
      overflow: hidden;
      /* 调试：整个容器 */
      border: 3px solid #FF0000 !important;
    }

    /* 顶部装饰条 */
    .top-decoration {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 120px;
      background: linear-gradient(135deg, var(--theme-color) 0%, var(--theme-color) 70%, transparent 70%);
      z-index: 1;
      /* 调试：顶部装饰条 */
      border: 2px solid #00FF00 !important;
    }

    /* 头部区域 */
    .resume-header {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30px 40px 20px 40px;
      margin-bottom: 0;
      /* 调试：头部区域 */
      background-color: rgba(255, 255, 0, 0.3) !important;
      border: 2px solid #FFFF00 !important;
    }

    .resume-title {
      font-size: 28px;
      font-weight: bold;
      color: var(--theme-color);
      letter-spacing: 2px;
      /* 调试：标题 */
      background-color: rgba(255, 0, 255, 0.2) !important;
      border: 1px solid #FF00FF !important;
    }

    .icon-group {
      display: flex;
      align-items: center;
      gap: 15px;
      /* 调试：图标组 */
      background-color: rgba(0, 255, 255, 0.2) !important;
      border: 1px solid #00FFFF !important;
    }

    .icon-circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: var(--theme-color);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      /* 调试：单个图标 */
      border: 1px solid #FFA500 !important;
    }

    .icon-circle i {
      color: white;
      font-size: 16px;
    }

    /* 各部分通用样式 */
    .section {
      margin: 30px 40px;
      position: relative;
      /* 调试：每个section */
      background-color: rgba(128, 128, 128, 0.1) !important;
      border: 2px solid #808080 !important;
    }

    .title-wrapper {
      position: relative;
      margin-bottom: 20px;
      height: 40px;
      display: flex;
      align-items: center;
      /* 调试：标题包装器 */
      background-color: rgba(255, 192, 203, 0.3) !important;
      border: 1px solid #FFC0CB !important;
    }

    .title-bg {
      position: absolute;
      left: 0;
      top: 0;
      height: 40px;
      width: 200px;
      background: linear-gradient(135deg, var(--theme-color) 0%, var(--theme-color) 85%, transparent 85%);
      z-index: 1;
      /* 调试：标题背景 */
      border: 1px solid #8B0000 !important;
    }

    .title {
      position: relative;
      z-index: 2;
      font-size: 16px;
      font-weight: bold;
      color: white;
      padding: 10px 20px;
      letter-spacing: 1px;
      /* 调试：标题文字 */
      background-color: rgba(0, 0, 139, 0.2) !important;
      border: 1px solid #00008B !important;
    }

    /* 标题后的横线 */
    .title-wrapper::after {
      content: '';
      position: absolute;
      left: 200px;
      top: 50%;
      right: 0;
      height: 1px;
      background-color: var(--light-gray);
      z-index: 0;
      /* 调试：横线 */
      background-color: #FF6347 !important;
      height: 3px !important;
    }

    /* 基本信息区域 */
    .basic-info {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 20px;
      background-color: var(--light-gray);
      padding: 20px;
      border-radius: 8px;
      /* 调试：基本信息区域 */
      background-color: rgba(173, 216, 230, 0.5) !important;
      border: 2px solid #ADD8E6 !important;
    }

    .info-content {
      flex: 1;
      margin-right: 30px;
      /* 调试：信息内容 */
      background-color: rgba(144, 238, 144, 0.3) !important;
      border: 1px solid #90EE90 !important;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px 30px;
      width: 100%;
      /* 调试：信息网格 */
      background-color: rgba(255, 218, 185, 0.3) !important;
      border: 1px solid #FFDAB9 !important;
    }

    .info-item {
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      display: flex;
      align-items: center;
      /* 调试：信息项 */
      background-color: rgba(240, 230, 140, 0.3) !important;
      border: 1px solid #F0E68C !important;
      padding: 2px;
    }

    .info-label {
      font-weight: bold;
      min-width: 80px;
      color: var(--secondary-color);
      /* 调试：信息标签 */
      background-color: rgba(221, 160, 221, 0.3) !important;
      border: 1px solid #DDA0DD !important;
    }

    .avatar {
      width: 120px;
      height: 160px;
      object-fit: cover;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      flex-shrink: 0;
      /* 调试：头像 */
      border: 3px solid #FF1493 !important;
    }

    .content {
      margin-bottom: 20px;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      /* 调试：内容区域 */
      background-color: rgba(250, 240, 230, 0.5) !important;
      border: 1px solid #FAF0E6 !important;
      padding: 5px;
    }

    /* 求职意向样式 */
    .job-intention-content {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px 30px;
      /* 调试：求职意向内容 */
      background-color: rgba(255, 160, 122, 0.3) !important;
      border: 1px solid #FFA07A !important;
    }

    .job-intention-item {
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      display: flex;
      align-items: center;
      /* 调试：求职意向项 */
      background-color: rgba(255, 228, 196, 0.5) !important;
      border: 1px solid #FFE4C4 !important;
      padding: 2px;
    }

    /* 教育经历样式 */
    .education-header {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 10px;
      padding: 15px;
      background-color: var(--light-gray);
      border-radius: 6px;
      /* 调试：教育经历头部 */
      background-color: rgba(176, 196, 222, 0.5) !important;
      border: 2px solid #B0C4DE !important;
    }

    .school {
      font-weight: bold;
      flex: 1;
      font-size: var(--base-font-size);
      color: var(--text-color);
      /* 调试：学校 */
      background-color: rgba(255, 105, 180, 0.2) !important;
      border: 1px solid #FF69B4 !important;
    }

    .major-degree {
      font-weight: bold;
      flex: 1;
      text-align: center;
      font-size: var(--base-font-size);
      color: var(--text-color);
      /* 调试：专业学位 */
      background-color: rgba(255, 20, 147, 0.2) !important;
      border: 1px solid #FF1493 !important;
    }

    .edu-date {
      flex: 1;
      text-align: right;
      color: var(--theme-color);
      font-size: var(--base-font-size);
      font-weight: bold;
      /* 调试：教育日期 */
      background-color: rgba(255, 0, 255, 0.2) !important;
      border: 1px solid #FF00FF !important;
    }

    /* 工作经历和实习经历样式 */
    .internship-header {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 10px;
      padding: 15px;
      background-color: var(--light-gray);
      border-radius: 6px;
      /* 调试：实习/工作经历头部 */
      background-color: rgba(152, 251, 152, 0.5) !important;
      border: 2px solid #98FB98 !important;
    }

    .company {
      font-weight: bold;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      /* 调试：公司名 */
      background-color: rgba(255, 255, 224, 0.5) !important;
      border: 1px solid #FFFFE0 !important;
    }

    .description {
      color: var(--text-color);
      margin-top: 10px;
      padding: 0 15px;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      /* 调试：描述 */
      background-color: rgba(230, 230, 250, 0.5) !important;
      border: 1px solid #E6E6FA !important;
    }

    /* 技能特长、获奖证书、兴趣爱好样式 */
    .skill, .award, .interest {
      display: block;
      margin-bottom: 8px;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      position: relative;
      padding-left: 20px;
      /* 调试：技能/奖项/兴趣 */
      background-color: rgba(255, 182, 193, 0.3) !important;
      border: 1px solid #FFB6C1 !important;
    }

    /* 自我评价样式 */
    .evaluation-content {
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      /* 调试：自我评价内容 */
      background-color: rgba(255, 239, 213, 0.5) !important;
      border: 1px solid #FFEFD5 !important;
    }

    .evaluation-content p {
      margin-bottom: 12px;
      position: relative;
      padding-left: 20px;
      /* 调试：自我评价段落 */
      background-color: rgba(250, 235, 215, 0.5) !important;
      border: 1px solid #FAEBD7 !important;
    }

    /* 隐藏空模块 */
    .hidden {
      display: none;
    }

    /* 调试信息显示 */
    .debug-info {
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 9999;
      max-width: 300px;
    }

    /* 打印样式 */
    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      .debug-info {
        display: none;
      }

      body {
        background-color: white;
        padding: 0;
      }

      .resume-container {
        width: 210mm;
        height: 297mm;
        padding: 10mm;
        margin: 0;
        box-shadow: none;
      }
    }
  </style>
</head>
<body>
  <!-- 调试信息面板 -->
  <div class="debug-info">
    <h4>🔧 调试模式</h4>
    <p><strong>红色边框</strong>: 整个简历容器</p>
    <p><strong>绿色边框</strong>: 顶部装饰条</p>
    <p><strong>黄色背景</strong>: 头部区域</p>
    <p><strong>粉色背景</strong>: 标题包装器</p>
    <p><strong>蓝色背景</strong>: 基本信息区域</p>
    <p><strong>灰色背景</strong>: 各个section</p>
    <p>每个元素都有不同颜色的边框和背景，方便识别和调整</p>
  </div>

  <div class="resume-container">
    <!-- 顶部装饰条 -->
    <div class="top-decoration"></div>

    <!-- 头部区域 -->
    <header class="resume-header">
      <h1 class="resume-title">个人简历</h1>
      <div class="icon-group">
        <div class="icon-circle"><i class="fas fa-download"></i></div>
        <div class="icon-circle"><i class="fas fa-share-alt"></i></div>
        <div class="icon-circle"><i class="fas fa-print"></i></div>
      </div>
    </header>

    <!-- 基本信息 -->
    
    <section class="section">
      <div class="title-wrapper">
        <div class="title-bg"></div>
        <div class="title">基本信息</div>
      </div>
      <div class="basic-info">
        <div class="info-content">
          <div class="info-grid">
            <div class="info-item"><span class="info-label">姓　名：</span>张三</div>
            <div class="info-item"><span class="info-label">求职意向：</span>前端开发工程师</div>
            <div class="info-item"><span class="info-label">民　族：</span>男</div>
            <div class="info-item"><span class="info-label">出生年月：</span>25年 xx月 xx日</div>
            <div class="info-item"><span class="info-label">籍　贯：</span>13800138000</div>
            
            <div class="info-item"><span class="info-label">电　话：</span><EMAIL></div>
            
            <div class="info-item"><span class="info-label">邮　箱：</span><EMAIL></div>
            
          </div>
        </div>
        
      </div>
    </section>
    

    <!-- 根据moduleOrders排序显示各个模块 -->
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">教育背景</div>
        </div>
        
        <div class="content">
          <div class="education-header">
            <div class="school">北京大学</div>
            <div class="major-degree">计算机科学/本科</div>
            <div class="edu-date">2016-09 - 2020-06</div>
          </div>
          <div class="description">
            
          </div>
        </div>
        
      </section>
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">技能证书</div>
        </div>
        <div class="content">
          
          <div class="skill">HTML/CSS/JavaScript</div>
          
          <div class="skill">Vue.js/React</div>
          
          <div class="skill">Node.js</div>
          
        </div>
      </section>
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
  </div>
</body>
</html>