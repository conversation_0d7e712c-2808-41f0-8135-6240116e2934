#!/usr/bin/env python3
"""
测试模板切换功能的脚本
用于验证不同模板ID是否能正确渲染不同的模板
"""

import requests
import json
import sys

# 服务器配置
BASE_URL = "http://localhost:18080"

# 测试数据
test_resume_data = {
    "basicInfo": {
        "name": "张三",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "gender": "男",
        "age": "25",
        "address": "北京市朝阳区",
        "photoUrl": ""
    },
    "jobIntention": {
        "position": "软件工程师",
        "salary": "15-20K",
        "city": "北京",
        "jobType": "全职"
    },
    "education": [
        {
            "school": "北京大学",
            "major": "计算机科学与技术",
            "degree": "本科",
            "startDate": "2018-09",
            "endDate": "2022-06",
            "description": "主修计算机科学与技术，GPA 3.8/4.0"
        }
    ],
    "work": [
        {
            "company": "腾讯科技",
            "position": "前端开发工程师",
            "startDate": "2022-07",
            "endDate": "至今",
            "description": "负责微信小程序开发，参与多个项目的前端架构设计"
        }
    ],
    "project": [
        {
            "projectName": "简历生成系统",
            "role": "技术负责人",
            "startDate": "2023-01",
            "endDate": "2023-06",
            "description": "基于FastAPI和Vue.js开发的在线简历生成系统"
        }
    ],
    "skills": [
        "Python - 熟练使用Python进行后端开发",
        "JavaScript - 熟练使用JavaScript进行前端开发"
    ],
    "moduleOrders": {
        "education": 1,
        "work": 2,
        "project": 3,
        "skills": 4
    }
}

# 测试主题配置
test_theme_config = {
    "themeColor": "#2E75B6",
    "fontSize": 12,
    "spacing": 1.2
}

def test_template_switch():
    """测试模板切换功能"""
    print("=== 开始测试模板切换功能 ===")

    # 测试的模板列表
    templates_to_test = [
        "templateA01",
        "templateA02",
        "templateA03",
        "templateA04"
    ]

    for template_id in templates_to_test:
        print(f"\n--- 测试模板: {template_id} ---")

        # 准备请求数据
        request_data = {
            "resume_data": test_resume_data,
            "template_id": template_id,
            "theme_config": test_theme_config
        }

        try:
            # 发送预览请求
            response = requests.post(
                f"{BASE_URL}/resume/preview",
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                html_length = len(result.get("html", ""))
                print(f"✅ 模板 {template_id} 渲染成功")
                print(f"   HTML长度: {html_length}")
                print(f"   返回的模板ID: {result.get('template_id')}")
                print(f"   主题配置: {result.get('theme_config')}")

                # 保存HTML到文件用于检查
                with open(f"test_output_{template_id}.html", "w", encoding="utf-8") as f:
                    f.write(result.get("html", ""))
                print(f"   已保存到: test_output_{template_id}.html")

            else:
                print(f"❌ 模板 {template_id} 渲染失败")
                print(f"   状态码: {response.status_code}")
                print(f"   错误信息: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")

def test_export_jpeg():
    """测试JPEG导出功能"""
    print("\n=== 测试JPEG导出功能 ===")

    template_id = "templateA03"
    print(f"测试模板: {template_id}")

    request_data = {
        "resume_data": test_resume_data,
        "template_id": template_id,
        "theme_config": test_theme_config
    }

    try:
        response = requests.post(
            f"{BASE_URL}/resume/export-jpeg",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=60
        )

        if response.status_code == 200:
            print(f"✅ JPEG导出成功")
            print(f"   文件大小: {len(response.content)} bytes")

            # 保存JPEG文件
            with open(f"test_export_{template_id}.jpeg", "wb") as f:
                f.write(response.content)
            print(f"   已保存到: test_export_{template_id}.jpeg")

        else:
            print(f"❌ JPEG导出失败")
            print(f"   状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 导出失败: {e}")

def check_server_health():
    """检查服务器健康状态"""
    print("=== 检查服务器状态 ===")

    try:
        # 检查主服务
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            print("✅ 主服务运行正常")
        else:
            print(f"❌ 主服务异常: {response.status_code}")
            return False

        # 检查模板列表
        response = requests.get(f"{BASE_URL}/resume/templates", timeout=10)
        if response.status_code == 200:
            templates = response.json().get("templates", [])
            print(f"✅ 可用模板: {templates}")
        else:
            print(f"❌ 获取模板列表失败: {response.status_code}")

        return True

    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

if __name__ == "__main__":
    print("简历模板切换功能测试脚本")
    print("=" * 50)

    # 检查服务器状态
    if not check_server_health():
        print("服务器不可用，请先启动服务")
        sys.exit(1)

    # 测试模板切换
    test_template_switch()

    # 测试导出功能
    test_export_jpeg()

    print("\n=== 测试完成 ===")
    print("请检查生成的文件和日志输出")
