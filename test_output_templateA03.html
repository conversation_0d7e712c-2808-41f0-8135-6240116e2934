<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>张三的简历</title>
  <link rel="stylesheet" href="http://localhost:18080/static/fontawesome/css/all.min.css" />
  <style>
    :root {
      --theme-color: #2E75B6;
      --base-font-size: 11pt;
      --max-font-size: 12pt;
      --spacing: 1.2;
      --text-color: #333333;
      --secondary-text-color: #555555;
      --border-color: #e0e0e0;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; */
      font-family: 'Alibaba PuHuiTi', 'Noto Sans SC';
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 10px 0;
      overflow-x: hidden;
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
    }

    /* Header Section */
    .resume-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--theme-color);
      padding: 15px 30px;
      color: white;
    }

    .resume-header-title {
      font-size: calc(var(--base-font-size) * 2);
      font-weight: bold;
      letter-spacing: 2px;
    }

    .resume-header-icons {
      display: flex;
      gap: 20px;
    }

    .resume-header-icons i {
      font-size: calc(var(--base-font-size) * 1.8);
      display: inline-block !important;
      width: auto !important;
      height: auto !important;
    }

    /* Basic Info Section */
    .basic-info-section {
      padding: 20px 30px;
      display: grid;
      grid-template-columns: 1fr auto; /* Main content | Photo */
      gap: 25px;
      border-bottom: 1px solid var(--border-color);
      align-items: start;
    }

    .basic-info-left {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .basic-info-name h2 {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }

    .basic-info-name h2 span {
        font-size: calc(var(--base-font-size) *1.8);
        font-weight: bold;
        color: #000000;
    }
    .basic-info-name h2::after {
      content: "";
      flex-grow: 1;
      height: 1px;
      margin-left: 10px;
      background-color: var(--theme-color);
    }

    .basic-info-details {
      display: grid;
      grid-template-columns: repeat(2, minmax(200px, 1fr)); /* Responsive columns */
      gap: 8px 15px; /* Row and column gap */
      font-size: var(--base-font-size);
      color: var(--secondary-text-color);
      background-color: rgb(255, 255, 255);
    }

    .basic-info-details p {
      display: flex;
      align-items: baseline;
    }

    .basic-info-details p span:first-child {
      font-weight: bold;
      min-width: 100px; /* Adjusted for labels like "政治面貌" */
      text-align: justify;
      color: var(--text-color);
      /* text-align: center; remove this to align left */
      background-color: rgb(255, 255, 255);
      margin-right: 5px; /* Add some space between label and value */
    }
     .basic-info-details p span:last-child {
       word-break: break-all;
     }

    .basic-info-photo {
      width: 110px;
      height: 154px;
      object-fit: cover;
      border: 1px solid var(--border-color);
      align-self: center;
    }

    /* General Section Styling */
    .section {
      padding: 5px 25px;
      margin-bottom: 0px;
    }

    .section-header {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      background-color: rgb(255, 255, 255);
    }

    .section-header i {
      margin-right: 10px;
      width: 18px;
      font-size: calc(var(--base-font-size) * 1.1);
      color: #fff; /* Icon color inside title */
      display: inline-block; /* 确保图标正确显示 */
    }

    .section-title {
        display: flex;
        align-items: center;
        font-size: calc(var(--base-font-size) * 1.3);
        background-color: var(--theme-color);
        border-radius: 6px;
        padding: 6px 15px;
        color: #fff;
        margin-right:10px; /* Space between title and line */
    }

    /* 确保Font Awesome图标正确显示 */
    .section-title i {
        display: inline-block !important;
        width: auto !important;
        height: auto !important;
        margin-right: 8px;
    }
    .section-title h2 {
      font-weight: bold;
      font-size: calc(var(--base-font-size) * 1.3);
      color: #fff; /* Ensure h2 color is white */
    }

    .section-header-line { /* Renamed from ::after for clarity */
      flex-grow: 1;
      height: 1px;
      background-color: var(--theme-color);
    }

    .section-content {
      padding-left: 5px;
      background-color: rgb(255, 255, 255);
    }

    .section-item {
        margin-bottom: 5px;
    }
    .item-header {
        margin-bottom: 5px;
    }

    .section-item .three-column {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        font-weight: bold;
    }
    .section-item .two-column {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        font-weight: bold;
    }
    .item-header .date-range {
      color: var(--secondary-text-color);
      font-weight: normal;
      text-align: right; /* Align dates to the right */
    }

    .item-description p {
      color: var(--secondary-text-color);
      padding-left: 15px;
    }
    .item-description p strong {
        color: var(--text-color);
        margin-right: 5px;
    }

    .horizon-item-list {
        padding-left: 20px;
        display: flex;
        align-items: center;
        flex-wrap:wrap;
        gap: 15px; /* Reduced gap */
    }
    .horizon-item-list li {
        margin-bottom: 5px; /* Add some bottom margin for wrapped items */
        background-color: #f0f0f0; /* Light background for items */
        padding: 3px 8px;
        border-radius: 4px;
        font-size: calc(var(--base-font-size) * 0.95);
    }
     .long-text-item {
        margin-bottom: 10px;
        line-height: var(--spacing);
     }
     .long-text-item .date-prefix {
         font-weight: bold;
         margin-right: 8px;
         color: var(--text-color);
     }
     .long-text-item p, .long-text-item span:not(.date-prefix) {
         color: var(--secondary-text-color);
     }

    .hidden {
      display: none;
    }

    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }
      .resume-container {
        box-shadow: none;
        margin: 0;
        width: 100%;
        min-height: 0;
      }
      .section {
        padding: 10px 20px;
      }
      .basic-info-section {
         padding: 15px 20px;
      }
       .section-header {
         padding: 4px 10px;
         margin-bottom: 10px;
         break-inside: avoid;
         page-break-inside: avoid;
       }
       .section-item, .long-text-item, .horizon-item-list li {
          break-inside: avoid;
          page-break-inside: avoid;
       }
        .item-description p {
             orphans: 3;
             widows: 3;
        }
    }

    p, h1, h2, h3, span, li, div {
       word-wrap: break-word;
       overflow-wrap: break-word;
       max-width: 100%;
    }

    /* 确保所有Font Awesome图标正确显示 */
    .fa, .fas, .far, .fal, .fab {
      display: inline-block !important;
      font-style: normal !important;
      font-variant: normal !important;
      text-rendering: auto !important;
      line-height: 1 !important;
    }
  </style>
</head>
<body>
  <div class="resume-container">

    <header class="resume-header">
      <span class="resume-header-title">个人简历</span>
      <div class="resume-header-icons">
        <i class="fas fa-building" aria-hidden="true"></i>
        <i class="fas fa-graduation-cap" aria-hidden="true"></i>
        <i class="fas fa-seedling" aria-hidden="true"></i>
      </div>
    </header>

    
    <section class="basic-info-section">
      <div class="basic-info-left">
        <div class="basic-info-name">
            <h2>
                <span>张三</span>
            </h2>
        </div>
        <div class="basic-info-details">
          <p><span>性别:</span><span>男</span></p>
          <p><span>年龄:</span><span>25</span></p>
          <p><span>电话:</span><span>13800138000</span></p>
          <p><span>邮箱:</span><span><EMAIL></span></p>
          
          
          <p><span>求职意向:</span><span>软件工程师</span></p>
          
          
          
          
          
          
          
          
          
          
        </div>
      </div>
      
      <img class="basic-info-photo" src="data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22110%22%20height%3D%22154%22%20viewBox%3D%220%200%20110%20154%22%3E%3Crect%20fill%3D%22%23e0e0e0%22%20width%3D%22110%22%20height%3D%22154%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2214%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2255%22%20y%3D%2277%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E" alt="个人照片占位符">
      
    </section>
    

    
      


      
      <section class="section" id="education-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-graduation-cap" aria-hidden="true"></i>
            <h2>教育背景</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>北京大学</span>
              <span>计算机科学与技术 (本科)</span>
              <span class="date-range">2018-09 - 2022-06</span>
            </div>
            
            <div class="item-description">
              <p>主修计算机科学与技术，GPA 3.8/4.0</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      
      <section class="section" id="work-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-briefcase" aria-hidden="true"></i>
            <h2>工作经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>腾讯科技</span>
              <span>前端开发工程师</span>
              <span class="date-range">2022-07 - 至今</span>
            </div>
            
            <div class="item-description">
              <p>负责微信小程序开发，参与多个项目的前端架构设计</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      
      <section class="section" id="project-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-project-diagram" aria-hidden="true"></i>
            <h2>项目经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>简历生成系统</span>
              <span>技术负责人</span>
              <span class="date-range">2023-01 - 2023-06</span>
            </div>
            
            <div class="item-description">
              <p>基于FastAPI和Vue.js开发的在线简历生成系统</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      
      <section class="section" id="skills-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-certificate" aria-hidden="true"></i>
            <h2>技能证书</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          <ul class="horizon-item-list">
            
            <li>Python - 熟练使用Python进行后端开发</li>
            
            <li>JavaScript - 熟练使用JavaScript进行前端开发</li>
            
          </ul>
        </div>
      </section>
      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
  </div>
</body>
</html>