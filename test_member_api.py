"""
测试会员功能API
"""
import requests
import json
import mysql.connector
from mysql.connector import Error

# 服务器配置
BASE_URL = "http://localhost:18080"

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def test_server_health():
    """测试服务器健康状态"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"服务器状态: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"服务器连接失败: {e}")
        return False

def test_member_status_without_auth():
    """测试未认证情况下查询会员状态"""
    try:
        response = requests.get(f"{BASE_URL}/auth/member-status")
        print(f"未认证查询会员状态: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 401:
            print("✅ 正确返回401未认证错误")
            return True
        return False
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def create_test_user():
    """在数据库中创建测试用户"""
    connection = None
    cursor = None
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 删除可能存在的测试用户
        delete_sql = "DELETE FROM users WHERE openid = 'test_openid_123'"
        cursor.execute(delete_sql)
        
        # 创建测试用户（非会员）
        insert_sql = """
        INSERT INTO users (openid, nickname, is_member) 
        VALUES ('test_openid_123', '测试用户', FALSE)
        """
        cursor.execute(insert_sql)
        connection.commit()
        
        print("✅ 创建测试用户成功")
        return True
        
    except Error as e:
        print(f"创建测试用户失败: {e}")
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()

def update_user_member_status(openid, is_member):
    """更新用户会员状态"""
    connection = None
    cursor = None
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        update_sql = "UPDATE users SET is_member = %s WHERE openid = %s"
        cursor.execute(update_sql, (is_member, openid))
        connection.commit()
        
        status_text = "会员" if is_member else "非会员"
        print(f"✅ 更新用户 {openid} 为{status_text}状态")
        return True
        
    except Error as e:
        print(f"更新用户状态失败: {e}")
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()

def get_test_token():
    """获取测试用户的token（模拟登录）"""
    # 这里需要实际的微信登录流程，暂时返回None
    # 在实际测试中，需要先通过微信登录获取token
    print("⚠️  需要真实的微信登录来获取token")
    print("   可以通过以下方式测试：")
    print("   1. 使用微信开发者工具获取真实code")
    print("   2. 调用 /auth/login 接口获取token")
    print("   3. 使用获取的token测试 /auth/member-status 接口")
    return None

def test_member_status_with_auth(token):
    """测试带认证的会员状态查询"""
    if not token:
        print("⚠️  没有有效的token，跳过认证测试")
        return False
    
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BASE_URL}/auth/member-status", headers=headers)
        print(f"认证查询会员状态: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 用户 {data.get('openid')} 会员状态: {data.get('is_member')}")
            print(f"   状态描述: {data.get('message')}")
            return True
        return False
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def show_api_usage():
    """显示API使用说明"""
    print("\n" + "="*50)
    print("会员状态查询API使用说明")
    print("="*50)
    print("接口: GET /auth/member-status")
    print("认证: 需要Bearer Token")
    print("响应格式:")
    print(json.dumps({
        "is_member": True,
        "openid": "用户openid",
        "message": "会员用户"
    }, indent=2, ensure_ascii=False))
    print("\n微信小程序调用示例:")
    print("""
wx.request({
  url: 'https://your-domain.com/auth/member-status',
  method: 'GET',
  header: {
    'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
  },
  success: (res) => {
    if (res.statusCode === 200) {
      const isMember = res.data.is_member;
      console.log('用户会员状态:', isMember ? '会员' : '非会员');
    }
  }
});
    """)

def main():
    """主测试函数"""
    print("=== 开始测试会员功能API ===")
    
    # 1. 测试服务器健康状态
    print("\n1. 测试服务器健康状态")
    if not test_server_health():
        print("❌ 服务器未启动，请先启动服务器")
        return
    
    # 2. 创建测试用户
    print("\n2. 创建测试用户")
    create_test_user()
    
    # 3. 测试未认证查询
    print("\n3. 测试未认证查询会员状态")
    test_member_status_without_auth()
    
    # 4. 更新用户为会员状态
    print("\n4. 更新测试用户为会员")
    update_user_member_status('test_openid_123', True)
    
    # 5. 更新用户为非会员状态
    print("\n5. 更新测试用户为非会员")
    update_user_member_status('test_openid_123', False)
    
    # 6. 获取token并测试认证查询
    print("\n6. 测试认证查询")
    token = get_test_token()
    test_member_status_with_auth(token)
    
    # 7. 显示API使用说明
    show_api_usage()
    
    print("\n=== 测试完成 ===")
    print("✅ 数据库字段添加成功")
    print("✅ API接口创建成功")
    print("⚠️  需要真实的微信登录token进行完整测试")

if __name__ == "__main__":
    main()
