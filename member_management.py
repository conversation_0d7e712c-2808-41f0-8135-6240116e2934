"""
会员状态管理工具
用于管理用户的会员状态
"""
import mysql.connector
from mysql.connector import Error
import logging
import sys

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def list_all_users():
    """列出所有用户及其会员状态"""
    connection = get_db_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor()
        
        query = """
        SELECT id, openid, nickname, is_member, created_at 
        FROM users 
        ORDER BY created_at DESC
        """
        
        cursor.execute(query)
        users = cursor.fetchall()
        
        if not users:
            logger.info("没有找到用户")
            return
        
        logger.info("用户列表:")
        logger.info("-" * 80)
        logger.info(f"{'ID':<5} {'OpenID':<20} {'昵称':<15} {'会员状态':<10} {'创建时间':<20}")
        logger.info("-" * 80)
        
        for user in users:
            user_id, openid, nickname, is_member, created_at = user
            member_status = "会员" if is_member else "非会员"
            nickname = nickname or "未设置"
            logger.info(f"{user_id:<5} {openid:<20} {nickname:<15} {member_status:<10} {created_at}")
        
        logger.info("-" * 80)
        logger.info(f"总计: {len(users)} 个用户")
        
    except Error as e:
        logger.error(f"查询用户列表失败: {e}")
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def update_member_status(openid, is_member):
    """更新用户会员状态"""
    connection = get_db_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        # 检查用户是否存在
        check_query = "SELECT id, nickname FROM users WHERE openid = %s"
        cursor.execute(check_query, (openid,))
        user = cursor.fetchone()
        
        if not user:
            logger.error(f"用户 {openid} 不存在")
            return False
        
        user_id, nickname = user
        
        # 更新会员状态
        update_query = "UPDATE users SET is_member = %s WHERE openid = %s"
        cursor.execute(update_query, (is_member, openid))
        connection.commit()
        
        status_text = "会员" if is_member else "非会员"
        logger.info(f"成功更新用户 {nickname or openid} (ID: {user_id}) 为{status_text}")
        return True
        
    except Error as e:
        logger.error(f"更新会员状态失败: {e}")
        connection.rollback()
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def get_member_stats():
    """获取会员统计信息"""
    connection = get_db_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor()
        
        # 总用户数
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]
        
        # 会员数
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_member = TRUE")
        member_count = cursor.fetchone()[0]
        
        # 非会员数
        non_member_count = total_users - member_count
        
        # 会员比例
        member_ratio = (member_count / total_users * 100) if total_users > 0 else 0
        
        logger.info("会员统计信息:")
        logger.info("-" * 40)
        logger.info(f"总用户数: {total_users}")
        logger.info(f"会员数: {member_count}")
        logger.info(f"非会员数: {non_member_count}")
        logger.info(f"会员比例: {member_ratio:.2f}%")
        logger.info("-" * 40)
        
    except Error as e:
        logger.error(f"获取统计信息失败: {e}")
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def search_user(keyword):
    """搜索用户"""
    connection = get_db_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor()
        
        query = """
        SELECT id, openid, nickname, is_member, created_at 
        FROM users 
        WHERE openid LIKE %s OR nickname LIKE %s
        ORDER BY created_at DESC
        """
        
        search_pattern = f"%{keyword}%"
        cursor.execute(query, (search_pattern, search_pattern))
        users = cursor.fetchall()
        
        if not users:
            logger.info(f"没有找到包含 '{keyword}' 的用户")
            return
        
        logger.info(f"搜索结果 (关键词: {keyword}):")
        logger.info("-" * 80)
        logger.info(f"{'ID':<5} {'OpenID':<20} {'昵称':<15} {'会员状态':<10} {'创建时间':<20}")
        logger.info("-" * 80)
        
        for user in users:
            user_id, openid, nickname, is_member, created_at = user
            member_status = "会员" if is_member else "非会员"
            nickname = nickname or "未设置"
            logger.info(f"{user_id:<5} {openid:<20} {nickname:<15} {member_status:<10} {created_at}")
        
        logger.info("-" * 80)
        logger.info(f"找到 {len(users)} 个用户")
        
    except Error as e:
        logger.error(f"搜索用户失败: {e}")
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def show_help():
    """显示帮助信息"""
    print("""
会员状态管理工具使用说明:

命令格式:
  python member_management.py <command> [arguments]

可用命令:
  list                    - 列出所有用户及其会员状态
  stats                   - 显示会员统计信息
  search <keyword>        - 搜索用户（按openid或昵称）
  upgrade <openid>        - 将用户升级为会员
  downgrade <openid>      - 将用户降级为非会员
  help                    - 显示此帮助信息

示例:
  python member_management.py list
  python member_management.py stats
  python member_management.py search test
  python member_management.py upgrade oxxxxxxxxxxxxxx
  python member_management.py downgrade oxxxxxxxxxxxxxx
    """)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == "list":
        list_all_users()
    elif command == "stats":
        get_member_stats()
    elif command == "search":
        if len(sys.argv) < 3:
            logger.error("请提供搜索关键词")
            return
        keyword = sys.argv[2]
        search_user(keyword)
    elif command == "upgrade":
        if len(sys.argv) < 3:
            logger.error("请提供用户openid")
            return
        openid = sys.argv[2]
        update_member_status(openid, True)
    elif command == "downgrade":
        if len(sys.argv) < 3:
            logger.error("请提供用户openid")
            return
        openid = sys.argv[2]
        update_member_status(openid, False)
    elif command == "help":
        show_help()
    else:
        logger.error(f"未知命令: {command}")
        show_help()

if __name__ == "__main__":
    main()
