#!/usr/bin/env python3
"""
测试 templateA01.html 模板渲染
"""

from app.schemas.resume import ResumeData, BasicInfo, JobIntention, EducationItem, WorkItem, ProjectItem, SchoolExperienceItem, InternshipItem, CustomItem
from app.services.resume_renderer import ResumeRenderer

# 创建测试数据
test_resume_data = {
    "moduleOrders": {
        "basicInfo": 0,
        "jobIntention": 1,
        "education": 2,
        "school": 3,
        "internship": 4,
        "work": 5,
        "project": 6,
        "skills": 7,
        "awards": 8,
        "interests": 9,
        "evaluation": 10,
        "custom1": 11,
        "custom2": 12,
        "custom3": 13
    },
    "basicInfo": {
        "name": "张三",
        "gender": "男",
        "phone": "13800138000",
        "photoUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDE1MCAyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgZmlsbD0iI2YwZjBmMCIgd2lkdGg9IjE1MCIgaGVpZ2h0PSIyMDAiLz48dGV4dCBmaWxsPSIjODg4IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZHk9Ii4zZW0iIHRleHQtYW5jaG9yPSJtaWRkbGUiIHg9Ijc1IiB5PSIxMDAiPueFp+eJhzwvdGV4dD48L3N2Zz4=",
        "city": "北京",
        "email": "<EMAIL>",
        "wechat": "zhangsan123",
        "age": "28",
        "birthday": "1995-01-01",
        "marriage": "未婚",
        "politics": "群众",
        "nation": "汉族",
        "hometown": "北京",
        "height": "175",
        "weight": "70",
        "educationLevel": "本科"
    },
    "jobIntention": {
        "position": "Python后端工程师",
        "city": "北京",
        "salary": "15k-20k",
        "status": "在职，考虑机会"
    },
    "education": [
        {
            "school": "北京大学",
            "major": "计算机科学与技术",
            "degree": "本科",
            "startDate": "2014-09",
            "endDate": "2018-07",
            "description": "主修课程：数据结构、算法、计算机网络、操作系统、数据库系统等"
        }
    ],
    "school": [
        {
            "role": "学生会主席",
            "startDate": "2016-09",
            "endDate": "2017-06",
            "content": "负责组织学生活动，协调各部门工作"
        }
    ],
    "internship": [
        {
            "company": "腾讯科技",
            "position": "实习开发工程师",
            "startDate": "2017-07",
            "endDate": "2018-01",
            "content": "参与Web应用开发，编写单元测试，协助解决生产环境问题"
        }
    ],
    "work": [
        {
            "company": "ABC科技有限公司",
            "position": "Python开发工程师",
            "startDate": "2018-07",
            "endDate": "至今",
            "description": "负责公司内部系统的开发和维护，使用FastAPI开发RESTful API，参与系统架构设计和优化"
        }
    ],
    "project": [
        {
            "projectName": "企业内部管理系统",
            "role": "后端开发",
            "startDate": "2019-03",
            "endDate": "2019-12",
            "description": "使用FastAPI和SQLAlchemy开发后端API，设计和优化数据库结构，实现认证和权限控制"
        }
    ],
    "skills": [
        "Python", "FastAPI", "Flask", "Django", 
        "SQLAlchemy", "MySQL", "PostgreSQL", 
        "Redis", "Docker", "Git"
    ],
    "awards": [
        "2017年校级编程比赛一等奖",
        "2016年数学建模竞赛二等奖"
    ],
    "interests": [
        "编程", "读书", "旅行", "摄影"
    ],
    "evaluation": [
        {
            "content": "我是一名有4年工作经验的Python后端开发工程师，擅长使用FastAPI、SQLAlchemy等框架进行Web应用开发。热爱编程，对新技术有浓厚兴趣，能够快速学习和适应。工作认真负责，善于团队协作，有良好的沟通能力。"
        }
    ],
    "custom1": [
        {
            "customName": "社会实践",
            "role": "志愿者",
            "startDate": "2016-07",
            "endDate": "2016-08",
            "content": "参与社区志愿服务活动"
        }
    ],
    "custom2": [],
    "custom3": []
}

def test_template_rendering():
    """测试模板渲染"""
    try:
        # 创建简历数据对象
        resume_data = ResumeData(**test_resume_data)
        
        # 创建渲染器
        renderer = ResumeRenderer()
        
        # 主题配置
        theme_config = {
            "theme_color": "#4A90E2",
            "base_font_size": 11,
            "spacing": 1.5
        }
        
        # 渲染模板
        html_content = renderer.render_template(resume_data, "templateA01", theme_config)
        
        # 保存到文件
        with open("output/test_templateA01.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        
        print("✅ 模板渲染成功！")
        print(f"📄 HTML文件已保存到: output/test_templateA01.html")
        print(f"📏 HTML内容长度: {len(html_content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板渲染失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import os
    
    # 确保输出目录存在
    os.makedirs("output", exist_ok=True)
    
    # 运行测试
    success = test_template_rendering()
    
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n💥 测试失败！")
