#!/usr/bin/env python3
"""
A01模板调试工具
生成带有彩色边框和背景的调试版本，方便手动调整元素位置
"""

import requests
import json
import os

# 服务器配置
BASE_URL = "http://localhost:18080"

# 测试数据
test_resume_data = {
    "moduleOrders": {
        "basicInfo": 1,
        "jobIntention": 2,
        "education": 3,
        "school": 4,
        "internship": 5,
        "work": 6,
        "project": 7,
        "skills": 8,
        "awards": 9,
        "interests": 10,
        "evaluation": 11,
        "custom1": 12,
        "custom2": 13,
        "custom3": 14
    },
    "basicInfo": {
        "name": "张三",
        "gender": "男",
        "phone": "13800138000",
        "city": "北京",
        "email": "<EMAIL>",
        "wechat": "zhangsan_wx",
        "age": "25",
        "birthday": "1998-01-01",
        "marriage": "未婚",
        "politics": "群众",
        "nation": "汉族",
        "hometown": "北京市",
        "height": "175",
        "weight": "70",
        "photoUrl": "",
        "educationLevel": "本科",
        "customTitle1": "",
        "customContent1": "",
        "customTitle2": "",
        "customContent2": ""
    },
    "jobIntention": {
        "position": "前端开发工程师",
        "location": "北京",
        "salary": "10k-15k",
        "status": "在职-考虑机会",
        "jobType": "全职"
    },
    "education": [
        {
            "school": "北京大学",
            "major": "计算机科学与技术",
            "degree": "本科",
            "startDate": "2016-09",
            "endDate": "2020-06",
            "description": "数据结构、算法设计、操作系统、计算机网络、数据库原理、软件工程",
            "sortIndex": 0
        }
    ],
    "school": [
        {
            "role": "学生会主席",
            "startDate": "2018-09",
            "endDate": "2019-06",
            "content": "组织策划各类校园活动，提升学生会影响力和凝聚力"
        }
    ],
    "internship": [
        {
            "startDate": "2019-07",
            "endDate": "2019-09",
            "company": "字节跳动",
            "position": "前端开发实习生",
            "content": "参与今日头条Web端开发，负责用户界面优化和交互体验提升"
        }
    ],
    "work": [
        {
            "startDate": "2020-07",
            "endDate": "至今",
            "company": "腾讯科技",
            "position": "前端开发工程师",
            "description": "负责微信小程序开发，参与多个项目的前端架构设计"
        }
    ],
    "project": [
        {
            "projectName": "简历生成系统",
            "role": "前端负责人",
            "startDate": "2023-01",
            "endDate": "2023-06",
            "description": "基于Vue.js开发的在线简历生成系统，支持多种模板和实时预览"
        }
    ],
    "skills": [
        "熟练掌握HTML、CSS、JavaScript",
        "精通Vue.js、React框架开发",
        "熟悉Node.js后端开发",
        "掌握Git版本控制工具"
    ],
    "awards": [
        "2019年度优秀学生干部",
        "全国大学生程序设计竞赛三等奖",
        "校级奖学金获得者"
    ],
    "interests": [
        "编程技术研究",
        "开源项目贡献",
        "篮球运动",
        "阅读技术书籍"
    ],
    "evaluation": [
        {
            "content": "具有扎实的前端开发基础，熟练掌握主流前端技术栈。\n\n具备良好的团队协作能力和沟通能力，能够快速适应新环境。\n\n对新技术有强烈的学习兴趣，持续关注行业发展趋势。\n\n具有较强的问题分析和解决能力，能够独立完成复杂项目开发。",
            "moduleOrder": 11
        }
    ],
    "custom1": [],
    "custom2": [],
    "custom3": []
}

def test_debug_template():
    """测试调试版本的A01模板"""
    print("🔧 A01模板调试工具")
    print("=" * 50)

    # 检查服务器状态
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器响应异常")
            return
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        return

    # 测试调试模板
    print("\n=== 生成调试版本简历 ===")

    # 构建请求数据
    request_data = {
        "template_id": "templateA01_debug",  # 使用调试版本模板
        "resume_data": test_resume_data,
        "theme_config": {
            "themeColor": "#4A90E2",
            "fontSize": 12,
            "spacing": 1.4
        }
    }

    try:
        # 发送预览请求
        response = requests.post(
            f"{BASE_URL}/resume/preview",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )

        if response.status_code == 200:
            print("✅ 调试模板渲染成功")

            # 保存HTML文件
            output_file = "debug_templateA01.html"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(response.text)

            print(f"   已保存到: {output_file}")
            print(f"   HTML长度: {len(response.text)}")

            # 打开文件
            import webbrowser
            file_path = os.path.abspath(output_file)
            webbrowser.open(f"file://{file_path}")
            print(f"   已在浏览器中打开: {output_file}")

        else:
            print(f"❌ 调试模板渲染失败")
            print(f"   状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

    print("\n=== 调试说明 ===")
    print("🎨 调试版本特点：")
    print("   • 每个元素都有不同颜色的边框和背景")
    print("   • 右上角有调试信息面板")
    print("   • 可以清楚看到各个元素的边界")
    print("   • 方便识别和调整元素位置")

    print("\n🔍 元素识别：")
    print("   • 红色边框: 整个简历容器")
    print("   • 绿色边框: 顶部装饰条")
    print("   • 黄色背景: 头部区域")
    print("   • 粉色背景: 标题包装器")
    print("   • 蓝色背景: 基本信息区域")
    print("   • 灰色背景: 各个section")

    print("\n📝 调整建议：")
    print("   1. 根据边框颜色识别要调整的元素")
    print("   2. 在templateA01.html中找到对应的CSS类")
    print("   3. 调整margin、padding、position等属性")
    print("   4. 重新生成查看效果")

if __name__ == "__main__":
    test_debug_template()
