# A01模板调试指南

## 🎯 调试工具概述

为了方便你手动调整A01模板的各个元素，我创建了一套完整的调试工具：

### 📁 调试文件
- `templateA01_debug.html` - 带有彩色边框的调试版本模板
- `debug_templateA01.html` - 生成的调试版本简历
- `debug_tool.html` - 可视化调试界面
- `test_debug_simple.py` - 快速生成调试版本的脚本

## 🎨 元素颜色标识系统

每个元素都有不同的颜色标识，方便你识别和调整：

| 颜色 | 元素 | CSS类名 | 说明 |
|------|------|---------|------|
| 🔴 红色边框 | 整个简历容器 | `.resume-container` | 最外层容器 |
| 🟢 绿色边框 | 顶部装饰条 | `.top-decoration` | 蓝色斜角装饰 |
| 🟡 黄色背景 | 头部区域 | `.resume-header` | 标题和图标区域 |
| 🟣 粉色背景 | 标题包装器 | `.title-wrapper` | 模块标题容器 |
| 🔵 蓝色背景 | 基本信息区域 | `.basic-info` | 个人信息展示区 |
| ⚫ 灰色背景 | 各个section | `.section` | 每个内容模块 |
| 🟠 橙色边框 | 图标圆圈 | `.icon-circle` | 右上角功能图标 |
| 🟤 棕色背景 | 信息项 | `.info-item` | 基本信息的每一项 |

## 🔧 使用方法

### 1. 生成调试版本
```bash
cd /home/<USER>/resume_serv
python test_debug_simple.py
```

### 2. 打开调试界面
在浏览器中打开 `debug_tool.html`

### 3. 查看调试版本
在浏览器中打开 `debug_templateA01.html`

### 4. 识别要调整的元素
- 根据颜色边框找到目标元素
- 查看右上角的调试信息面板
- 记录对应的CSS类名

### 5. 修改模板文件
在 `app/templates/templateA01.html` 中找到对应的CSS类，进行调整

### 6. 重新生成测试
运行脚本重新生成，查看调整效果

## 📐 常用调整属性

### 位置调整
```css
/* 绝对定位 */
position: absolute;
top: 10px;
left: 20px;
right: 30px;
bottom: 40px;

/* 相对定位 */
position: relative;
margin: 10px 20px;
padding: 15px 25px;
```

### 尺寸调整
```css
width: 200px;
height: 100px;
max-width: 100%;
min-height: 50px;
```

### 布局调整
```css
/* Flexbox */
display: flex;
justify-content: center;
align-items: center;
gap: 15px;

/* Grid */
display: grid;
grid-template-columns: 1fr 1fr;
gap: 20px;
```

### 装饰调整
```css
background-color: #f5f5f5;
border: 1px solid #ddd;
border-radius: 8px;
box-shadow: 0 2px 8px rgba(0,0,0,0.1);
```

## 🎯 重点调整区域

### 1. 顶部装饰条 (`.top-decoration`)
```css
.top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px; /* 调整高度 */
  background: linear-gradient(135deg, var(--theme-color) 0%, var(--theme-color) 70%, transparent 70%);
}
```

### 2. 头部区域 (`.resume-header`)
```css
.resume-header {
  padding: 30px 40px 20px 40px; /* 调整内边距 */
  margin-bottom: 0; /* 调整下边距 */
}
```

### 3. 模块标题 (`.title-wrapper`, `.title-bg`)
```css
.title-bg {
  height: 40px; /* 调整高度 */
  width: 200px; /* 调整宽度 */
  background: linear-gradient(135deg, var(--theme-color) 0%, var(--theme-color) 85%, transparent 85%);
}
```

### 4. 基本信息区域 (`.basic-info`)
```css
.basic-info {
  margin-bottom: 20px; /* 调整下边距 */
  padding: 20px; /* 调整内边距 */
  border-radius: 8px; /* 调整圆角 */
}
```

### 5. 内容区域间距 (`.section`)
```css
.section {
  margin: 30px 40px; /* 调整外边距 */
}
```

## 🔍 调试技巧

### 1. 使用浏览器开发者工具
- 按F12打开开发者工具
- 使用元素选择器定位具体元素
- 实时修改CSS查看效果

### 2. 渐进式调整
- 先调整大的布局结构
- 再调整细节样式
- 每次只调整一个属性

### 3. 备份原始文件
```bash
cp app/templates/templateA01.html app/templates/templateA01_backup.html
```

### 4. 使用CSS变量
模板中定义了CSS变量，可以统一调整：
```css
:root {
  --theme-color: #4A90E2;
  --base-font-size: 12pt;
  --spacing: 1.4;
  --light-gray: #F5F5F5;
}
```

## 📱 响应式调整

### 打印样式
```css
@media print {
  .resume-container {
    width: 210mm;
    height: 297mm;
    padding: 10mm;
  }
}
```

### 屏幕适配
```css
@media (max-width: 768px) {
  .resume-container {
    width: 100%;
    padding: 15px;
  }
}
```

## 🚀 快速命令

### 生成调试版本
```bash
python test_debug_simple.py
```

### 生成正常版本（用于对比）
```bash
# 修改test_debug_simple.py中的template_id为"templateA01"
python test_debug_simple.py
```

### 查看文件差异
```bash
diff debug_templateA01.html normal_templateA01.html
```

## 💡 调试建议

1. **先整体后细节**：先调整大的布局，再调整细节
2. **保持一致性**：确保各模块的间距和样式保持一致
3. **测试不同内容**：用不同长度的内容测试布局
4. **检查边界情况**：测试空内容、超长内容的显示效果
5. **验证打印效果**：确保打印时样式正确

## 🔄 调试流程

1. 🎯 **识别问题**：在调试版本中找到需要调整的元素
2. 📝 **定位代码**：根据颜色标识找到对应的CSS类
3. ✏️ **修改样式**：在templateA01.html中调整CSS
4. 🔄 **重新生成**：运行脚本生成新的调试版本
5. 👀 **查看效果**：检查调整是否达到预期
6. 🔁 **重复优化**：重复上述步骤直到满意

## 📞 需要帮助？

如果在调试过程中遇到问题，可以：
1. 查看浏览器控制台的错误信息
2. 对比调试版本和正常版本的差异
3. 使用浏览器开发者工具实时调试
4. 参考CSS文档和最佳实践

祝你调试顺利！🎉
