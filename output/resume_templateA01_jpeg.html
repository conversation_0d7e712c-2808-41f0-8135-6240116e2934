<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>李华的简历</title>
  <link rel="stylesheet" href="http://localhost:18080/static/fontawesome/css/all.min.css">
  <style>
    :root {
      --theme-color: #44546b;
      --theme-color-light: #E8F4FD;
      --base-font-size: 11pt;
      --max-font-size: 12pt;
      --spacing: 1.2;
      --text-color: #333333;
      --secondary-color: #666666;
      --light-gray: #F5F5F5;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 10px 0;
      overflow-x: hidden;
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      padding: 0;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      position: relative;
      overflow: hidden;
    }

    /* 顶部装饰条 */
    .top-decoration {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 120px;
      background: linear-gradient(135deg, var(--theme-color) 0%, var(--theme-color) 70%, transparent 70%);
      z-index: 1;
    }

    /* 头部区域 */
    .resume-header {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30px 40px 20px 40px;
      margin-bottom: 0;
    }

    .resume-title {
      font-size: 28px;
      font-weight: bold;
      color: var(--theme-color);
      letter-spacing: 2px;
    }

    .icon-group {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .icon-circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: var(--theme-color);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .icon-circle i {
      color: white;
      font-size: 16px;
    }

    /* 各部分通用样式 */
    .section {
      margin: 30px 40px;
      position: relative;
    }

    .title-wrapper {
      position: relative;
      margin-bottom: 20px;
      height: 40px;
      display: flex;
      align-items: center;
    }

    .title-bg {
      position: absolute;
      left: 0;
      top: 0;
      height: 40px;
      width: 200px;
      background: linear-gradient(135deg, var(--theme-color) 0%, var(--theme-color) 85%, transparent 85%);
      z-index: 1;
    }

    .title {
      position: relative;
      z-index: 2;
      font-size: 16px;
      font-weight: bold;
      color: white;
      padding: 10px 20px;
      letter-spacing: 1px;
    }

    /* 标题后的横线 */
    .title-wrapper::after {
      content: '';
      position: absolute;
      left: 200px;
      top: 50%;
      right: 0;
      height: 1px;
      background-color: var(--light-gray);
      z-index: 0;
    }

    /* 基本信息区域 */
    .basic-info {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 20px;
      background-color: var(--light-gray);
      padding: 20px;
      border-radius: 8px;
    }

    .info-content {
      flex: 1;
      margin-right: 30px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px 30px;
      width: 100%;
    }

    .info-item {
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      display: flex;
      align-items: center;
    }

    .info-label {
      font-weight: bold;
      min-width: 80px;
      color: var(--secondary-color);
    }

    .avatar {
      width: 120px;
      height: 160px;
      object-fit: cover;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      flex-shrink: 0;
    }

    .content {
      margin-bottom: 20px;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
    }

    /* 求职意向样式 */
    .job-intention-content {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px 30px;
    }

    .job-intention-item {
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      display: flex;
      align-items: center;
    }

    .job-intention-item .info-label {
      font-weight: bold;
      min-width: 80px;
      color: var(--secondary-color);
    }

    /* 教育经历样式 */
    .education-header {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 10px;
      padding: 15px;
      background-color: var(--light-gray);
      border-radius: 6px;
    }

    .school {
      font-weight: bold;
      flex: 1;
      font-size: var(--base-font-size);
      color: var(--text-color);
    }

    .major-degree {
      font-weight: bold;
      flex: 1;
      text-align: center;
      font-size: var(--base-font-size);
      color: var(--text-color);
    }

    .edu-date {
      flex: 1;
      text-align: right;
      color: var(--theme-color);
      font-size: var(--base-font-size);
      font-weight: bold;
    }

    .edu-description {
      color: var(--text-color);
      margin-top: 10px;
      padding: 0 15px;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
    }

    .courses-label {
      font-weight: bold;
      color: var(--secondary-color);
    }

    /* 工作经历和实习经历样式 */
    .internship-header {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 10px;
      padding: 15px;
      background-color: var(--light-gray);
      border-radius: 6px;
    }

    .company {
      font-weight: bold;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .internship-header .company:first-child {
      flex: 1;
      color: var(--text-color);
    }

    .internship-header .company:nth-child(2) {
      flex: 1;
      text-align: center;
      color: var(--text-color);
    }

    .internship-header .time {
      flex: 1;
      text-align: right;
      color: var(--theme-color);
      font-size: var(--base-font-size);
      font-weight: bold;
    }

    .description {
      color: var(--text-color);
      margin-top: 10px;
      padding: 0 15px;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
    }

    /* 在校经历样式 */
    .school-experience-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 10px;
      padding: 15px;
      background-color: var(--light-gray);
      border-radius: 6px;
    }

    .activity {
      font-weight: bold;
      font-size: var(--base-font-size);
      color: var(--text-color);
      flex: 1;
    }

    .time {
      color: var(--theme-color);
      font-size: var(--base-font-size);
      text-align: right;
      font-weight: bold;
    }

    /* 技能特长、获奖证书、兴趣爱好样式 */
    .skill, .award, .interest {
      display: block;
      margin-bottom: 8px;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      position: relative;
      padding-left: 20px;
    }

    .skill::before, .award::before, .interest::before {
      content: "•";
      position: absolute;
      left: 0;
      color: var(--theme-color);
      font-weight: bold;
    }

    /* 自我评价样式 */
    .evaluation-content {
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
    }

    .evaluation-content p {
      margin-bottom: 12px;
      position: relative;
      padding-left: 20px;
    }

    .evaluation-content p::before {
      content: counter(eval-counter) ".";
      counter-increment: eval-counter;
      position: absolute;
      left: 0;
      color: var(--theme-color);
      font-weight: bold;
    }

    .evaluation-content {
      counter-reset: eval-counter;
    }

    /* 自定义模块样式 */
    .custom-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 10px;
      padding: 15px;
      background-color: var(--light-gray);
      border-radius: 6px;
    }

    .custom-name {
      font-weight: bold;
      font-size: var(--base-font-size);
      color: var(--text-color);
      flex: 1;
    }

    /* 隐藏空模块 */
    .hidden {
      display: none;
    }

    /* 打印样式 */
    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }

      .resume-container {
        width: 210mm;
        height: 297mm;
        padding: 10mm;
        margin: 0;
        box-shadow: none;
      }

      .section, .section-header {
        break-inside: avoid;
        page-break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="resume-container">
    <!-- 顶部装饰条 -->
    <div class="top-decoration"></div>

    <!-- 头部区域 -->
    <header class="resume-header">
      <h1 class="resume-title">个人简历</h1>
      <div class="icon-group">
        <div class="icon-circle"><i class="fas fa-download"></i></div>
        <div class="icon-circle"><i class="fas fa-share-alt"></i></div>
        <div class="icon-circle"><i class="fas fa-print"></i></div>
      </div>
    </header>

    <!-- 基本信息 -->
    
    <section class="section">
      <div class="title-wrapper">
        <div class="title-bg"></div>
        <div class="title">基本信息</div>
      </div>
      <div class="basic-info">
        <div class="info-content">
          <div class="info-grid">
            <div class="info-item"><span class="info-label">姓　名：</span>李华</div>
            <div class="info-item"><span class="info-label">求职意向：</span>高级前端开发工程师</div>
            <div class="info-item"><span class="info-label">民　族：</span>女</div>
            <div class="info-item"><span class="info-label">出生年月：</span>28年 xx月 xx日</div>
            <div class="info-item"><span class="info-label">籍　贯：</span>13912345678</div>
            <div class="info-item"><span class="info-label">身　高：</span>165cmcm</div>
            <div class="info-item"><span class="info-label">电　话：</span><EMAIL></div>
            <div class="info-item"><span class="info-label">学　历：</span>硕士</div>
            <div class="info-item"><span class="info-label">邮　箱：</span><EMAIL></div>
            <div class="info-item"><span class="info-label">政治面貌：</span>群众</div>
          </div>
        </div>
        
        <img class="avatar" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=" alt="个人照片" onerror="this.onerror=null; this.src='data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22150%22%20height%3D%22200%22%20viewBox%3D%220%200%20150%20200%22%3E%3Crect%20fill%3D%22%23f0f0f0%22%20width%3D%22150%22%20height%3D%22200%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2220%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2275%22%20y%3D%22100%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E'">
        
      </div>
    </section>
    

    <!-- 根据moduleOrders排序显示各个模块 -->
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">教育背景</div>
        </div>
        
        <div class="content">
          <div class="education-header">
            <div class="school">复旦大学</div>
            <div class="major-degree">软件工程/硕士</div>
            <div class="edu-date">2018-09 - 2021-06</div>
          </div>
          <div class="edu-description">
            <span class="courses-label">主修课程：</span>研究方向：前端框架优化，发表2篇核心期刊论文
          </div>
        </div>
        
        <div class="content">
          <div class="education-header">
            <div class="school">浙江大学</div>
            <div class="major-degree">计算机科学与技术/学士</div>
            <div class="edu-date">2014-09 - 2018-06</div>
          </div>
          <div class="edu-description">
            <span class="courses-label">主修课程：</span>GPA 3.8/4.0，ACM校队成员
          </div>
        </div>
        
      </section>
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">在校经历</div>
        </div>
        
        <div class="content">
          <div class="school-experience-header">
            <div class="activity">学生会主席</div>
            <div class="time">2016-09 - 2017-06</div>
          </div>
          
          <div class="description">组织校级技术竞赛，参与人数超过500人</div>
          
        </div>
        
      </section>
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">实习经历</div>
        </div>
        
        <div class="content">
          <div class="internship-header">
            <div class="company">腾讯科技</div>
            <div class="company">前端开发实习生</div>
            <div class="time">2020-07 - 2020-12</div>
          </div>
          
          <div class="description">参与微信小程序性能优化项目，首屏加载速度提升40%</div>
          
        </div>
        
      </section>
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">工作经历</div>
        </div>
        
        <div class="content">
          <div class="internship-header">
            <div class="company">阿里巴巴集团</div>
            <div class="company">前端开发工程师</div>
            <div class="time">2021-07 - 至今</div>
          </div>
          
          <div class="description">负责电商中台前端架构设计，主导Vue3+TypeScript技术栈落地</div>
          
        </div>
        
      </section>
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">项目经历</div>
        </div>
        
        <div class="content">
          <div class="internship-header">
            <div class="company">跨平台电商系统</div>
            <div class="company">技术负责人</div>
            <div class="time">2022-03 - 2023-01</div>
          </div>
          
          <div class="description">基于Flutter+Node.js的跨平台解决方案，日活用户突破100万</div>
          
        </div>
        
      </section>
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">技能证书</div>
        </div>
        <div class="content">
          
          <div class="skill">React/Vue</div>
          
          <div class="skill">TypeScript</div>
          
          <div class="skill">Webpack</div>
          
          <div class="skill">Node.js</div>
          
          <div class="skill">Python</div>
          
          <div class="skill">Docker</div>
          
          <div class="skill">Jenkins</div>
          
          <div class="skill">GitLab CI/CD</div>
          
        </div>
      </section>
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">奖项荣誉</div>
        </div>
        <div class="content">
          
          <div class="award">2022年阿里技术卓越奖</div>
          
          <div class="award">2021年QCon优秀演讲者</div>
          
          <div class="award">2020年全国大学生计算机设计大赛一等奖</div>
          
        </div>
      </section>
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">兴趣爱好</div>
        </div>
        <div class="content">
          
          <div class="interest">开源社区</div>
          
          <div class="interest">AI技术研究</div>
          
          <div class="interest">马拉松</div>
          
          <div class="interest">古典音乐</div>
          
        </div>
      </section>
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">自我评价</div>
        </div>
        <div class="content">
          <div class="description evaluation-content">
            
              
                
                  <p>技术视野广阔，具备复杂系统架构能力, 
团队管理经验丰富，培养多名中级工程师</p>
                
              
            
          </div>
        </div>
      </section>
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">自定义模块1</div>
        </div>
        
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">专利信息</div>
            <div class="time">2021-05 - 2021-05</div>
          </div>
          
          <div class="description">一种前端资源懒加载方法（专利号：ZL2021XXXXXXX）</div>
          
        </div>
        
      </section>
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">自定义模块2</div>
        </div>
        
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">职业认证</div>
            <div class="time">2022-10 - 2025-10</div>
          </div>
          
          <div class="description">AWS Certified Solutions Architect</div>
          
        </div>
        
      </section>
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">自定义模块3</div>
        </div>
        
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">技术培训</div>
            <div class="time">2023-01 - 2023-03</div>
          </div>
          
          <div class="description">Vue3高级特性培训（参与人数：120人）</div>
          
        </div>
        
      </section>
      
    
  </div>
</body>
</html>