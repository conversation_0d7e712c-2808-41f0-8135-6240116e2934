<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    张三的简历
  </title>
  <link rel="stylesheet" href="http://localhost:18080/static/fontawesome/css/all.min.css">
  <style>
    /* 全局样式 */
    :root {
      /* 动态主题配置变量 */
      --theme-color: #44546B;
      --base-font-size: 11pt;
      --max-font-size: 12pt;
      --spacing: 1.2;
      --text-color: #000000;
      --secondary-color: #e44c4c;
    }


    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background-color: #ffffff;
      display: flex;
      justify-content: center;
      padding: 0px;
      overflow-x: hidden; /* 防止水平滚动 */
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      overflow: hidden; /* 防止内容溢出 */
      font-size: var(--base-font-size);
      line-height: var(--spacing);
    }

    .resume-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--theme-color);
      padding: 10px 20px;
      width: 100%;
    }

    /* SVG图标的通用样式 */
    .header-icons svg{
      filter: brightness(0) invert(1);
    }


    /* 为左侧栏的图标设置黑色 */
    .left-column-title svg.icon-1,
    .left-column-title img.icon-1 {
      width: 26px;
      height: 26px;
      margin-right: 10px;
      filter: none;
      color: black; /* 为使用currentColor的图标设置颜色 */
    }

    /* 图标尺寸 */
    .header-icons .icon-1 {
      width: 26px;
      height: 26px;
      color: white;
      vertical-align: middle;
      margin-right: 10px;
    }

    .header-icons .icon-2 {
      width: 20px;
      height: 20px;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
    }

    .header-icons .icon-3 {
      width: 14px;
      height: 14px;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
    }

    .header-icons .icon-4 {
      width: 8px;
      height: 8px;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
    }

    .left-column .icon-1 {
      width: 26px;
      height: 26px;
      margin-right: 10px;
    }

    .content {
      display: grid;
      grid-template-columns: 3fr 7fr; /* 左右分栏设为3:7比例 */
      width: 100%;
      min-height: 0; /* 修复IE中的grid内容溢出问题 */
    }

    /* 左侧栏样式 */
    .left-column {
      background-color: #f0f0f0;
      padding: 0 10px 20px;
      overflow: hidden; /* 防止内容溢出 */
      box-sizing: border-box; /* 确保padding不会增加宽度 */
      height: 100%;
      min-height: calc(297mm - 45px); /* A4页面高度减去标题高度 */
      position: relative; /* 添加相对定位 */
    }

    /* 更新左侧栏图标样式 */
    .left-column .icon-1 {
      width: 26px;
      height: 26px;
      margin-right: 10px;
    }

    .photo-container {
      padding: 15px;
      margin: 15px;
      display: flex;
      justify-content: center;
    }

    .photo-container img {
      width: 130px; /* 缩小照片尺寸 */
      height: 180px;
      object-fit: cover;
      border: 1px solid #f0f0f0;
    }

    .name-container {
      text-align: center;
      padding: 10px 0;
      margin: 15px;
    }

    .name-container h1 {
      font-size: calc(var(--base-font-size) * 1.8); /* 调整字体大小 */
      font-weight: bold;
      word-break: break-word; /* 允许长名字换行 */
    }

    /* 右侧栏样式 */
    .right-column {
      padding: 15px;
      overflow: hidden; /* 防止内容溢出 */
      box-sizing: border-box; /* 确保padding不会增加宽度 */
    }

    /* 通用部分样式 */
    .section {
      margin-bottom: 10px;
      overflow: hidden; /* 防止内容溢出 */
    }

    .section-tile-and-line {
      display: grid;
      grid-template-columns: auto 1fr; /* 标题自适应宽度，线条占据剩余空间 */
      align-items: center;
      background-color: #ffffff;
      margin-bottom: 10px;
      gap: 10px; /* 设置标题和线条之间的间距 */
    }

    .section-title {
      display: flex;
      align-items: center;
      padding: 8px 15px;
      background-color: var(--theme-color);
      color: white;
      border-radius: 4px;
      width: auto; /* 自动宽度 */
    }

    .section-title i {
      margin-right: 10px;
      font-size: calc(var(--base-font-size) * 1.2);
    }

    .section-title h2 {
      font-size: calc(var(--base-font-size) * 1.2);
      font-weight: normal;
      white-space: nowrap; /* 防止文字换行 */
    }

    .section-title-line {
      height: 1px;
      background-color: var(--theme-color);
      margin-right: 10px;
    }

    .section-content {
      padding: 0 10px;
      color: black;
      word-wrap: break-word; /* 允许长词换行 */
      overflow-wrap: break-word; /* 现代浏览器支持 */
    }

    .left-column .section-content {
      font-size: min(var(--base-font-size), var(--max-font-size));
      line-height: var(--spacing);
    }

    .left-column .section-content p {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 25px; /* 添加行间距 */
    }

    .left-column .section-content p span:first-child {
      width: 75px;
      font-weight: bold;
      white-space: nowrap; /* 防止字段名称换行 */
      flex-shrink: 0; /* 防止字段名称被压缩 */
    }

    #contact-section-content p span:first-child {
      width: auto;
      font-weight: bold;
      white-space: nowrap; /* 防止字段名称换行 */
      flex-shrink: 0; /* 防止字段名称被压缩 */
    }

    .left-column-strip {
      width: 100%;
      height: 18px;
      background-color: var(--theme-color);
      border-radius: 4px;
    }

    .left-column-title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px 15px;
      margin-bottom: 10px;
    }

    /* 替换i元素样式为img元素样式 */
    .left-column-title i {
      margin-right: 10px;
      font-size: calc(var(--base-font-size) * 1.2);
    }

    .left-column-title h2 {
      font-size: calc(var(--base-font-size) * 1.2);
      font-weight: bold;
      color: black;
    }

    /* 教育背景样式 */
    .education-header {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 5px; /* 添加列间距 */
      margin-bottom: 10px;
      font-weight: bold;
    }

    .education-item {
      margin-bottom: 10px;
    }

    .education-courses {
      line-height: var(--spacing);
    }

    /* 在校经历样式 */
    .experience-item {
      margin-bottom: 5px;
      position: relative;
      padding-left: 20px;
    }

    .experience-item:before {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 8px;
      height: 8px;
      background-color: var(--theme-color);
      border-radius: 50%;
    }

    /* 奖项荣誉样式 兴趣爱好样式 技能证书样式 */
    .award-item, .skill-item, .interest-item {
        padding-left: 20px;
        display: flex;
        align-items: center;
        flex-wrap:wrap;
      }
      .award-item div, .skill-item div, .interest-item div {
        margin-right: 25px;
      }


    /* 自我评价样式 */
    .evaluation-item {
      margin-bottom: 10px;
      position: relative;
      padding-left: 20px;
    }

    .evaluation-item:before {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 8px;
      height: 8px;
      background-color: var(--theme-color);
      border-radius: 50%;
    }

    /* 隐藏空模块 */
    .hidden {
      display: none;
    }

    /* 确保内容超长时可以自动换行 */
    p, h1, h2, h3, span {
      max-width: 100%;
      overflow-wrap: break-word;
      word-wrap: break-word;
    }

    /* 列表样式 */
    .item-list {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    .item-list li {
      margin-bottom: 10px;
      position: relative;
      padding-left: 20px;
    }

    .item-list li:before {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 8px;
      height: 8px;
      background-color: var(--theme-color);
      border-radius: 50%;
    }

    /* 打印媒体查询 */
    @page {
      size: A4;
      margin: 0;
      padding: 3mm 3mm;
    }

    @media print {
      .resume-container {
        box-shadow: none;
        margin: 0;
        overflow: hidden;
      }

      .section-tile-and-line {
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .section-content {
        orphans: 3;
        widows: 3;
      }
    }
  </style>
</head>

<body>
  <div class="resume-container">
    <!-- 简历头部 -->
    <header class="resume-title">
      <div class="header-icons">
        <!-- 使用SVG use标签复用图标并控制大小 -->
        <img class="icon-1" src="http://localhost:18080/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
        <img class="icon-2" src="http://localhost:18080/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
        <img class="icon-3" src="http://localhost:18080/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
        <img class="icon-4" src="http://localhost:18080/static/resume-images/templateA02/chiLun.png" alt="图标" onerror="this.style.display='none';" />
      </div>
      <div class="header-image-right">
        <img src="http://localhost:18080/static/resume-images/templateA02/personal_resume.png" alt="图标" onerror="this.style.display='none';" />
      </div>
    </header>

    <div class="content">
      <!-- 左侧栏 -->
      <div class="left-column">
        <!-- 照片区域 -->
        <div class="photo-container">
          <img id="avatar" src="" alt="个人照片" loading="eager" onerror="this.onerror=null; this.src='data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22130%22%20height%3D%22180%22%20viewBox%3D%220%200%20130%20180%22%3E%3Crect%20fill%3D%22%23f0f0f0%22%20width%3D%22130%22%20height%3D%22180%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2220%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2265%22%20y%3D%2290%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E'">
        </div>

        <!-- 姓名 -->
        <div class="name-container">
          <h1 id="name">张三</h1>
        </div>

        <!-- 联系方式 -->
        <div class="section " id="contact-section">
          <div class="left-column-strip"></div>
          <div class="left-column-title">
            <!-- 使用SVG use标签复用联系方式图标 -->
            <img class="icon-1" src="http://localhost:18080/static/resume-images/templateA02/tel.svg" alt="联系方式图标" onerror="this.style.display='none';" />

            <h2>联系方式</h2>
          </div>
          <div class="section-content" id="contact-section-content">
            <p><span>电话：</span><span id="phone">1231313123</span></p>
            <p><span>邮箱：</span><span id="email"><EMAIL></span></p>
            
          </div>
        </div>

        <!-- 个人信息 -->
        <div class="section " id="personal-info-section">
          <div class="left-column-strip"></div>
          <div class="left-column-title">
            <!-- 使用SVG use标签复用个人信息图标 -->
            <img class="icon-1" src="http://localhost:18080/static/resume-images/templateA02/info.svg" alt="个人信息图标" onerror="this.style.display='none';" />

            <h2>个人信息</h2>
          </div>
          <div class="section-content">
            <p><span>性别：</span><span id="gender">男</span></p>
            
            
            
            
            
            
            
            
            
            
          </div>
        </div>
      </div>

      <!-- 右侧栏 -->
      <div class="right-column">
        <!-- 求职意向 -->
        <div class="section " id="job-intention-section">
          <div class="section-tile-and-line">
            <div class="section-title">
              <i class="fas fa-bullseye"></i>
              <h2>求职意向</h2>
            </div>
            <div class="section-title-line"></div>
          </div>
          <div class="section-content" style="display: flex; gap:20px; flex-wrap:wrap;">
            <p><span>期望职位：</span><span id="position">总裁</span></p>
            <p><span>期望薪资：</span><span id="salary">5-7k</span></p>
            
            <p><span>求职状态：</span><span id="job-status">在职</span></p>
          </div>
        </div>

        <!-- 教育背景 -->
        <div class="section " id="education-section">
          <div class="section-tile-and-line">
            <div class="section-title">
              <i class="fas fa-graduation-cap"></i>
              <h2>教育背景</h2>
            </div>
            <div class="section-title-line"></div>
          </div>
          <div class="section-content" id="education-content">
            
              
                <div class="education-item">
                  <div class="education-header">
                    <span>2025-05-2025-05</span>
                    <span>交大</span>
                    <span>会计（本科）</span>
                  </div>
                  <div class="education-courses">
                    
                  </div>
                </div>
              
            
          </div>
        </div>

        <!-- 在校经历 -->
        <div class="section " id="school-experience-section">
          <div class="section-tile-and-line">
            <div class="section-title">
              <i class="fas fa-school"></i>
              <h2>在校经历</h2>
            </div>
            <div class="section-title-line"></div>
          </div>
          <div class="section-content" id="school-experience-content">
            
              
                <div class="experience-item">
                  <p>2025-05 </p>
                </div>
              
            
          </div>
        </div>

        <!-- 工作经历 -->
        <div class="section " id="work-section">
          <div class="section-tile-and-line">
            <div class="section-title">
              <i class="fas fa-briefcase"></i>
              <h2>工作经历</h2>
            </div>
            <div class="section-title-line"></div>
          </div>
          <div class="section-content" id="work-content">
            
              
                <div class="work-item experience-item">
                  <p><strong>胜多负少</strong> 2025-05 - 2025-05</p>
                  <p>职位：士大夫</p>
                  <p>工作内容：</p>
                </div>
              
            
          </div>
        </div>

        <!-- 项目经历 -->
        <div class="section " id="project-section">
          <div class="section-tile-and-line">
            <div class="section-title">
              <i class="fas fa-project-diagram"></i>
              <h2>项目经历</h2>
            </div>
            <div class="section-title-line"></div>
          </div>
          <div class="section-content" id="project-content">
            
              
                <div class="project-item experience-item">
                  <p><strong>是</strong> 2025-05 - 2025-05</p>
                  <p>角色：上市</p>
                  <p>项目描述：是</p>
                </div>
              
            
          </div>
        </div>

        <!-- 技能证书 -->
        <div class="section " id="skills-section">
          <div class="section-tile-and-line">
            <div class="section-title">
              <i class="fas fa-certificate"></i>
              <h2>技能证书</h2>
            </div>
            <div class="section-title-line"></div>
          </div>
          <div class="section-content" id="skills-content">
            <div class="skill-item">
              
                <div>是是是</div>
              
            </div>
          </div>
        </div>

        <!-- 奖项荣誉 -->
        <div class="section " id="awards-section">
          <div class="section-tile-and-line">
            <div class="section-title">
              <i class="fas fa-award"></i>
              <h2>奖项荣誉</h2>
            </div>
            <div class="section-title-line"></div>
          </div>
          <div class="section-content" id="awards-content">
            <div class="award-item">
              
                <div>第三方士大夫</div>
              
            </div>
          </div>
        </div>

        <!-- 兴趣爱好 -->
        <div class="section " id="interests-section">
          <div class="section-tile-and-line">
            <div class="section-title">
              <i class="fas fa-heart"></i>
              <h2>兴趣爱好</h2>
            </div>
            <div class="section-title-line"></div>
          </div>
          <div class="section-content" id="interests-content">
            <div class="interest-item">
              
                <div>呵呵哈哈哈</div>
              
            </div>
          </div>
        </div>

        <!-- 自我评价 -->
        <div class="section " id="self-evaluation-section">
          <div class="section-tile-and-line">
            <div class="section-title">
              <i class="fas fa-comment"></i>
              <h2>自我评价</h2>
            </div>
            <div class="section-title-line"></div>
          </div>
          <div class="section-content" id="self-evaluation-content">
            
              
                
                  <!-- 处理对象数组 -->
                  
                    <div class="evaluation-item">
                      <p>士大夫撒啊是是

s的方式
士大夫
sdf sdf wewerw werw

沃尔沃二人
沃尔沃二</p>
                    </div>
                  
                
              
            
          </div>
        </div>

        <!-- 自定义模块1 -->
        

        <!-- 自定义模块2 -->
        

        <!-- 自定义模块3 -->
        
      </div>
    </div>
  </div>
</body>
</html>