# 微信小程序简历服务后端启动说明

## 项目概述
这是一个基于FastAPI的微信小程序服务后端，提供用户认证、简历渲染、用户行为记录和反馈系统等功能。

## 环境要求
- Python 3.8+
- MySQL 8.0+
- Node.js 14+ (用于PDF服务)

## 快速启动

### 1. 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Node.js依赖（PDF服务）
cd pdf-service
npm install
cd ..
```

### 2. 配置数据库
```bash
# 启动MySQL服务
sudo systemctl start mysql

# 初始化数据库表
python init_database.py
```

### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
nano .env
```

重要配置项：
- `WECHAT_APP_ID`: 微信小程序AppID
- `WECHAT_APP_SECRET`: 微信小程序AppSecret
- `SECRET_KEY`: JWT密钥（生产环境必须更改）
- `DATABASE_URL`: 数据库连接字符串

### 4. 启动服务

#### 启动PDF服务
```bash
cd pdf-service
npm start
```

#### 启动主服务
```bash
# 开发模式
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 18080
```

### 5. 验证服务
```bash
# 运行测试脚本
python test_auth_api.py
```

## API文档
服务启动后，访问以下URL查看API文档：
- Swagger UI: http://localhost:18080/docs
- ReDoc: http://localhost:18080/redoc

## 主要API端点

### 认证相关
- `POST /auth/login` - 微信登录
- `GET /auth/user` - 获取用户信息
- `PUT /auth/user` - 更新用户信息
- `POST /auth/refresh` - 刷新token

### 简历相关
- `POST /resume/preview` - 预览简历
- `POST /resume/export-pdf` - 导出PDF
- `POST /resume/export-jpeg` - 导出JPEG
- `GET /resume/templates` - 获取模板列表

### 用户行为
- `POST /user/action` - 记录用户行为
- `GET /user/action` - 获取行为记录
- `GET /user/action/stats` - 获取行为统计

### 反馈系统
- `POST /feedback` - 提交反馈
- `GET /feedback` - 获取反馈列表
- `GET /feedback/{id}` - 获取反馈详情
- `POST /feedback/{id}/reply` - 管理员回复

## 数据库表结构

### users (用户表)
- id: 主键
- openid: 微信openid
- nickname: 用户昵称
- avatar_url: 头像URL
- 其他用户信息字段

### user_actions (用户行为记录表)
- id: 主键
- user_id: 用户ID
- action_type: 行为类型
- template_id: 模板ID
- action_content: 行为详细内容
- created_at: 创建时间

### feedback (反馈表)
- id: 主键
- user_id: 用户ID
- content: 反馈内容
- status: 反馈状态
- created_at: 创建时间

### feedback_replies (反馈回复表)
- id: 主键
- feedback_id: 反馈ID
- admin_name: 管理员名称
- reply_content: 回复内容
- created_at: 创建时间

## 微信小程序集成

### 登录流程
1. 小程序调用`wx.login()`获取code
2. 发送code到`/auth/login`接口
3. 服务器验证code并返回JWT token
4. 后续请求携带token进行认证

### 示例代码
```javascript
// 微信小程序登录
wx.login({
  success: (res) => {
    if (res.code) {
      // 发送登录请求
      wx.request({
        url: 'https://your-domain.com/auth/login',
        method: 'POST',
        data: {
          code: res.code,
          user_info: {
            nickName: '用户昵称',
            avatarUrl: '头像URL'
          }
        },
        success: (loginRes) => {
          // 保存token
          wx.setStorageSync('access_token', loginRes.data.access_token);
        }
      });
    }
  }
});
```

## 故障排除

### 常见问题
1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接字符串
   - 确认用户权限

2. **微信登录失败**
   - 检查微信小程序配置
   - 验证AppID和AppSecret
   - 确认code的有效性

3. **PDF服务不可用**
   - 检查Node.js服务是否启动
   - 验证端口3001是否被占用
   - 查看PDF服务日志

### 日志查看
```bash
# 查看主服务日志
tail -f logs/app.log

# 查看PDF服务日志
cd pdf-service
npm run logs
```

## 生产环境部署

### 安全配置
1. 更改默认密钥
2. 限制CORS来源
3. 配置HTTPS
4. 设置防火墙规则

### 性能优化
1. 配置数据库连接池
2. 启用缓存机制
3. 使用负载均衡
4. 监控系统资源

## 联系支持
如有问题，请查看开发文档或提交issue。
