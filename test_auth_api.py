"""
测试认证API功能
"""
import requests
import json
import time

# 服务器配置
BASE_URL = "http://localhost:18080"

def test_server_health():
    """测试服务器健康状态"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"服务器状态: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"服务器连接失败: {e}")
        return False

def test_api_docs():
    """测试API文档"""
    try:
        response = requests.get(f"{BASE_URL}/docs")
        print(f"API文档状态: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"API文档访问失败: {e}")
        return False

def test_mock_wechat_login():
    """测试模拟微信登录（不需要真实的微信code）"""
    login_data = {
        "code": "mock_code_for_testing",
        "user_info": {
            "nickName": "测试用户",
            "avatarUrl": "https://example.com/avatar.jpg",
            "gender": 1,
            "country": "中国",
            "province": "北京",
            "city": "北京"
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"登录请求状态: {response.status_code}")
        print(f"登录响应: {response.text}")
        
        if response.status_code == 400:
            print("预期的错误：微信登录需要真实的code")
            return True
        
        return False
    except Exception as e:
        print(f"登录测试失败: {e}")
        return False

def test_user_endpoints():
    """测试用户相关端点（无需认证）"""
    endpoints = [
        "/auth/user",
        "/user/action",
        "/feedback"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            print(f"端点 {endpoint} 状态: {response.status_code}")
            
            if response.status_code == 401:
                print(f"  预期的401错误：需要认证")
            elif response.status_code == 422:
                print(f"  预期的422错误：参数验证失败")
            else:
                print(f"  响应: {response.text[:100]}...")
                
        except Exception as e:
            print(f"端点 {endpoint} 测试失败: {e}")

def main():
    """主测试函数"""
    print("=== 开始API测试 ===")
    
    # 测试服务器健康状态
    print("\n1. 测试服务器健康状态")
    if not test_server_health():
        print("服务器未启动，请先启动服务器")
        return
    
    # 测试API文档
    print("\n2. 测试API文档")
    test_api_docs()
    
    # 测试模拟微信登录
    print("\n3. 测试模拟微信登录")
    test_mock_wechat_login()
    
    # 测试用户端点
    print("\n4. 测试用户端点")
    test_user_endpoints()
    
    print("\n=== 测试完成 ===")
    print("\n提示:")
    print("1. 访问 http://localhost:18080/docs 查看完整API文档")
    print("2. 要测试完整的微信登录功能，需要配置真实的微信小程序参数")
    print("3. 数据库表已创建，可以通过MySQL客户端查看")

if __name__ == "__main__":
    main()
