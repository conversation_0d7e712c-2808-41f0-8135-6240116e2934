<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简历预览</title>
  <style>
    :root {
      --theme-color: {{ theme_color|default('#2B6CB0') }};
      --base-font-size: {{ base_font_size|default('12pt') }};
      --font-size: var(--base-font-size);
      --spacing: {{ spacing|default('1.5') }};
      --text-color: {{ text_color|default('#333333') }};
      --secondary-color: {{ secondary_color|default('#666666') }};
    }
    @page {
      size: A4;
      margin: 0;
    }
    body {
      margin: 0;
      padding: 0;
      background: #f7f9fb;
    }
    .resumeTemplateA01 {
      position: relative;
      width: 210mm;
      min-height: 297mm;
      margin: 0 auto;
      padding: 10mm;
      box-sizing: border-box;
      font-size: var(--font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      page-break-after: always;
      background: #fff;
    }
    .resume-decoration-line {
      content: '';
      position: absolute;
      left: 13mm;
      top: calc(var(--base-font-size) + 4pt + 25mm);
      bottom: 10mm;
      width: 0.05mm;
      background-color: var(--theme-color);
      opacity: 0.8;
      z-index: 1;
    }
    .resume-header {
      margin-bottom: 5mm;
      display: flex;
      align-items: center;
      margin-top: 2mm;
    }
    .icons-group {
      display: flex;
      gap: 3mm;
      margin-left: 110mm;
      margin-top: -21pt;
    }
    .resume-title {
      font-size: 30pt;
      font-weight: bold;
      color: var(--theme-color);
      margin-top: -25pt;
      margin-bottom: 0pt;
      margin-left: -2mm;
      margin-right: 0pt;
    }
    .icon-circle {
      width: 10mm;
      height: 10mm;
      border-radius: 50%;
      background-color: var(--theme-color);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .icon-circle svg {
      width: 5mm;
      height: 5mm;
    }
    .decoration-line {
      width: 104%;
      margin: -10mm -10mm 15mm -10mm;
      padding: 0 10mm;
      text-align: left;
      margin-left: -23mm;
      margin-top: -3mm;
    }
    .decoration-line svg {
      width: 107%;
      height: 10mm;
    }
    .section {
      margin-bottom: 5mm;
      padding: 3mm;
      border-radius: 2mm;
      margin-top: -10mm;
      position: relative;
      background: #fff;
      overflow: hidden;
    }
    .section-basic-info {
      margin-top: -18mm;
      position: relative;
    }
    .basic-info-decoration {
      width: 100%;
      margin-top: -3.6mm;
      margin-left: -12mm;
      transform: scale(1.3);
      transform-origin: left center;
      color: var(--theme-color);
      position: absolute;
      z-index: 1;
      pointer-events: none;
    }
    .basic-info-decoration svg {
      width: 100%;
      height: auto;
      display: block;
      opacity: 0.8;
    }
    .title {
      position: relative;
      z-index: 2;
      font-size: calc(var(--base-font-size) + 4pt);
      line-height: 1;
      color: #FFFFFF;
      font-weight: bold;
      padding: 2mm 4mm;
      letter-spacing: 2pt;
      background: var(--theme-color);
      border-radius: 4px;
      display: inline-block;
      min-width: 120px;
      max-width: 90%;
      margin-bottom: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .content {
      margin-bottom: 3mm;
      margin-top: 3mm;
      padding: 0 5mm;
      font-size: calc(var(--base-font-size) + 2pt);
      line-height: var(--spacing);
      color: var(--text-color);
      gap: 1mm;
      display: flex;
      flex-wrap: wrap;
      letter-spacing: 1.2pt;
      position: relative;
      z-index: 2;
    }
    .info-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      width: 100%;
      position: relative;
      z-index: 2;
    }
    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 2.5mm;
      width: 100%;
      position: relative;
      z-index: 2;
    }
    .info-item {
      margin-bottom: 1.5mm;
      font-size: calc(var(--base-font-size) + 2pt);
      line-height: var(--spacing);
      color: var(--text-color);
    }
    .photo-wrapper {
      flex-shrink: 0;
      width: 35mm;
      height: 45mm;
      border: 0.35mm solid #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
    }
    .photo {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
    .education-header, .work-header, .internship-header, .project-header, .school-header, .custom-header {
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      margin-bottom: 2mm;
      width: 100%;
      min-height: 6mm;
    }
    .school, .company, .project-name, .custom-name, .activity {
      font-weight: bold;
      font-size: calc(var(--base-font-size) + 2pt);
      line-height: var(--spacing);
      color: var(--text-color);
      display: block;
    }
    .major-degree, .position, .project-role {
      color: var(--secondary-color);
      margin-bottom: 1.5mm;
      font-size: calc(var(--base-font-size) + 2pt);
      text-align: center;
      width: 40%;
    }
    .edu-date, .work-date, .intern-date, .project-date, .school-date, .time {
      width: 30%;
      text-align: right;
      color: var(--secondary-color);
      font-size: calc(var(--base-font-size) + 1pt);
      line-height: var(--spacing);
      font-weight: bold;
    }
    .description {
      color: var(--text-color);
      margin-top: 0.5mm;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: calc(var(--base-font-size) + 2pt);
      line-height: var(--spacing);
      width: 100%;
    }
    .skill-item, .award-item, .interest-item {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
      font-size: calc(var(--base-font-size) + 2pt);
      color: var(--text-color);
    }
    .courses {
      color: var(--text-color);
      margin-top: 1mm;
      font-size: calc(var(--base-font-size) + 2pt);
      line-height: var(--spacing, 1);
    }
    .courses-label {
      font-weight: bold;
      margin-right: 2mm;
    }
    /* 通用模块装饰样式 */
    .section-decoration {
      width: 100%;
      margin-top: -3.6mm;
      margin-left: -12mm;
      transform: scale(1.3);
      transform-origin: left center;
      color: var(--theme-color);
      position: absolute;
      z-index: 1;
      pointer-events: none;
    }

    .section-decoration svg {
      width: 100%;
      height: auto;
      display: block;
      opacity: 0.8;
    }

    .section-content {
      position: relative;
      z-index: 2;
    }
  </style>
</head>
<body>
<div class="resumeTemplateA01">
  <div class="resume-decoration-line"></div>
  <!-- 主标题 -->
  <div class="resume-header">
    <div class="resume-title">个人简历</div>
    <div class="icons-group">
      <div class="icon-circle">
        <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
          <path d="M946.2 553.66H766.9l0.35 73.56h-63.78l-0.35-73.56h-382l0.35 73.56h-63.78l-0.35-73.56H65v281.23a59.79 59.79 0 0 0 59.79 59.79h774.42A59.79 59.79 0 0 0 959 834.89V553.7q-6.4-0.03-12.8-0.04zM77.8 502.12h179.62l0.22-17.4a16 16 0 0 1 16-15.79h31.79a16 16 0 0 1 16 16.2l-0.21 17h381.83l0.22-17.4a16 16 0 0 1 16-15.79h31.78a16 16 0 0 1 16 16.2l-0.22 17h192V352.55a64 64 0 0 0-63.78-63.78H767.54V193.1a64 64 0 0 0-63.78-63.78H320.24a64 64 0 0 0-63.78 63.78v95.67H128.9a64 64 0 0 0-63.78 63.78v149.53q6.34 0.03 12.68 0.04z m243.27-308.91l0.11-0.11h381.64l0.11 0.11v95.56H321.07z" fill="#ffffff"></path>
        </svg>
      </div>
      <div class="icon-circle">
        <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
          <path d="M466.090667 148.181333a86.485333 86.485333 0 0 1 78.848 0.768L951.466667 362.026667a17.28 17.28 0 0 1-0.298667 30.805333l-405.76 203.093333a86.485333 86.485333 0 0 1-78.890667-0.768L60.032 382.037333a17.28 17.28 0 0 1 0.256-30.805333l405.802667-203.050667z M211.2 502.314667v193.109333c0 6.570667 3.712 12.544 9.557333 15.488l266.154667 132.992c11.861333 5.973333 25.813333 6.101333 37.845333 0.426667l281.856-133.546667a17.28 17.28 0 0 0 9.898667-15.616v-191.786667l-310.784 155.392-294.528-156.458666z" fill="#ffffff"></path>
        </svg>
      </div>
      <div class="icon-circle">
        <svg viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
          <path d="M1018.319924 112.117535q4.093748 9.210934 6.652341 21.492179t2.558593 25.585928-5.117186 26.609365-16.374994 25.585928q-12.281245 12.281245-22.003898 21.492179t-16.886712 16.374994q-8.187497 8.187497-15.351557 14.32812l-191.382739-191.382739q12.281245-11.257808 29.167958-27.121083t28.144521-25.074209q14.32812-11.257808 29.679676-15.863275t30.191395-4.093748 28.656239 4.605467 24.050772 9.210934q21.492179 11.257808 47.589826 39.402329t40.425766 58.847634zM221.062416 611.554845q6.140623-6.140623 28.656239-29.167958t56.289041-56.80076l74.710909-74.710909 82.898406-82.898406 220.038979-220.038979 191.382739 192.406177-220.038979 220.038979-81.874969 82.898406q-40.937484 39.914047-73.687472 73.175753t-54.242167 54.753885-25.585928 24.562491q-10.234371 9.210934-23.539054 19.445305t-27.632802 16.374994q-14.32812 7.16406-41.960921 17.398431t-57.824197 19.957024-57.312478 16.886712-40.425766 9.210934q-27.632802 3.070311-36.843736-8.187497t-5.117186-37.867173q2.046874-14.32812 9.722653-41.449203t16.374994-56.289041 16.886712-53.730448 13.304682-33.773425q6.140623-14.32812 13.816401-26.097646t22.003898-26.097646z" fill="#ffffff"></path>
        </svg>
      </div>
    </div>
  </div>
  <!-- 装饰线 -->
  <div class="decoration-line">
    <svg width="2000" height="25" xmlns="http://www.w3.org/2000/svg">
      <g>
        <rect stroke="#AAAAAA" id="svg_4" height="8" width="900" y="9.5" x="427" fill="#AAAAAA"/>
        <path stroke="var(--theme-color)" id="svg_5" d="m10,5l417,0l0,13l-417,0l0,-13z" fill="var(--theme-color)"/>
        <path stroke="var(--theme-color)" id="svg_6" d="m427,18l0,-13l11.4,13l-11.4,0z" fill="var(--theme-color)"/>
      </g>
    </svg>
  </div>

  <!-- 各模块内容 -->
  {% if resume.basicInfo %}
  <div class="section section-basic-info">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">基本信息</div>
      <div class="content">
        <div class="info-wrapper">
          <div class="info-grid">
            {% for key, value in resume.basicInfo.__dict__.items() if value and key not in ['photoUrl'] %}
            <div class="info-item">
              <span class="label">{{ key }}</span>
              <span class="value">{{ value }}</span>
            </div>
            {% endfor %}
          </div>
          {% if resume.basicInfo.photoUrl %}
          <div class="photo-wrapper">
            <img src="{{ resume.basicInfo.photoUrl }}" alt="证件照" class="photo" />
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 求职意向 -->
  {% if resume.jobIntention and (resume.jobIntention.position or resume.jobIntention.salary or resume.jobIntention.city or resume.jobIntention.status) %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">求职意向</div>
      <div class="content job-intention-content">
        {% if resume.jobIntention.position %}<div class="info-item">期望职位：{{ resume.jobIntention.position }}</div>{% endif %}
        {% if resume.jobIntention.salary %}<div class="info-item">期望薪资：{{ resume.jobIntention.salary }}</div>{% endif %}
        {% if resume.jobIntention.city %}<div class="info-item">期望城市：{{ resume.jobIntention.city }}</div>{% endif %}
        {% if resume.jobIntention.status %}<div class="info-item">求职状态：{{ resume.jobIntention.status }}</div>{% endif %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 教育经历 -->
  {% if resume.education and resume.education|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">教育经历</div>
      <div class="content">
        {% for edu in resume.education %}
        <div class="education-header">
          <div class="school">{{ edu.school }}</div>
          <div class="major-degree">{{ edu.major }}{% if edu.degree %} / {{ edu.degree }}{% endif %}</div>
          <div class="edu-date">{{ edu.startDate }} - {{ edu.endDate }}</div>
        </div>
        {% if edu.description %}<div class="description">{{ edu.description }}</div>{% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 在校经历 -->
  {% if resume.school and resume.school|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">在校经历</div>
      <div class="content">
        {% for schoolItem in resume.school %}
        <div class="school-header">
          <div class="activity">{{ schoolItem.role }}</div>
          <div class="time">{{ schoolItem.startDate }} - {{ schoolItem.endDate }}</div>
        </div>
        {% if schoolItem.content %}<div class="description">{{ schoolItem.content }}</div>{% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 实习经历 -->
  {% if resume.internship and resume.internship|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">实习经历</div>
      <div class="content">
        {% for intern in resume.internship %}
        <div class="internship-header">
          <div class="company">{{ intern.company }}</div>
          <div class="position">{{ intern.position }}</div>
          <div class="intern-date">{{ intern.startDate }} - {{ intern.endDate }}</div>
        </div>
        {% if intern.content %}<div class="description">{{ intern.content }}</div>{% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 工作经历 -->
  {% if resume.work and resume.work|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">工作经历</div>
      <div class="content">
        {% for work in resume.work %}
        <div class="work-header">
          <div class="company">{{ work.company }}</div>
          <div class="position">{{ work.position }}</div>
          <div class="work-date">{{ work.startDate }} - {{ work.endDate }}</div>
        </div>
        {% if work.description %}<div class="description">{{ work.description }}</div>{% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 项目经历 -->
  {% if resume.project and resume.project|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">项目经历</div>
      <div class="content">
        {% for proj in resume.project %}
        <div class="project-header">
          <div class="project-name">{{ proj.projectName }}</div>
          <div class="project-role">{{ proj.role }}</div>
          <div class="project-date">{{ proj.startDate }} - {{ proj.endDate }}</div>
        </div>
        {% if proj.description %}<div class="description">{{ proj.description }}</div>{% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 技能特长 -->
  {% if resume.skills and resume.skills|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">技能特长</div>
      <div class="content">
        {% for skill in resume.skills %}
        <span class="skill-item">{{ skill }}</span>
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 获奖证书 -->
  {% if resume.awards and resume.awards|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">获奖证书</div>
      <div class="content">
        {% for award in resume.awards %}
        <span class="award-item">{{ award }}</span>
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 兴趣爱好 -->
  {% if resume.interests and resume.interests|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">兴趣爱好</div>
      <div class="content">
        {% for interest in resume.interests %}
        <span class="interest-item">{{ interest }}</span>
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 自我评价 -->
  {% if resume.evaluation and resume.evaluation|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">自我评价</div>
      <div class="content">
        {% for eva in resume.evaluation %}
        <div class="description">{{ eva.content }}</div>
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 自定义模块1 -->
  {% if resume.custom1 and resume.custom1|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">自定义模块1</div>
      <div class="content">
        {% for item in resume.custom1 %}
        <div class="custom-header">
          <div class="custom-name">{{ item.customName }}</div>
          <div class="time">{{ item.startDate }} - {{ item.endDate }}</div>
        </div>
        {% if item.content %}<div class="description">{{ item.content }}</div>{% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 自定义模块2 -->
  {% if resume.custom2 and resume.custom2|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">自定义模块2</div>
      <div class="content">
        {% for item in resume.custom2 %}
        <div class="custom-header">
          <div class="custom-name">{{ item.customName }}</div>
          <div class="time">{{ item.startDate }} - {{ item.endDate }}</div>
        </div>
        {% if item.content %}<div class="description">{{ item.content }}</div>{% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
  <!-- 自定义模块3 -->
  {% if resume.custom3 and resume.custom3|length > 0 %}
  <div class="section">
    <div class="section-decoration">
      <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
        <g style="z-index: 5;">
          <title>Layer 1</title>
          <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" fill="var(--theme-color)" style="z-index: 5;"/>
          <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" style="z-index: 5;"/>
          <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" style="z-index: 5;"/>
          <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
        </g>
      </svg>
    </div>
    <div class="section-content">
      <div class="title">自定义模块3</div>
      <div class="content">
        {% for item in resume.custom3 %}
        <div class="custom-header">
          <div class="custom-name">{{ item.customName }}</div>
          <div class="time">{{ item.startDate }} - {{ item.endDate }}</div>
        </div>
        {% if item.content %}<div class="description">{{ item.content }}</div>{% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
  {% endif %}
</div>
</body>
</html> 