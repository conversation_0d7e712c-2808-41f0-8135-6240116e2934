from fastapi import APIRouter, HTTPException, Request, Query, Response, Body, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from sqlalchemy.orm import Session
from app.schemas.resume import ResumeData
from app.services.resume_renderer import ResumeRenderer
from app.services.pdf_service import PDFService
from app.database import get_db
from app.models import User, UserAction
from app.auth import get_current_user_optional, get_current_user
from typing import Optional, List
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/resume",
    tags=["resume"],
    responses={404: {"description": "未找到"}},
)

# 初始化简历渲染器和PDF服务
resume_renderer = ResumeRenderer()
pdf_service = PDFService()

def record_user_action(db: Session, user: Optional[User], action_type: str, template_id: Optional[str] = None, action_content: Optional[dict] = None):
    """记录用户行为"""
    if user:
        try:
            user_action = UserAction(
                user_id=user.id,
                action_type=action_type,
                template_id=template_id,
                action_content=action_content
            )
            db.add(user_action)
            db.commit()
            logger.info(f"记录用户 {user.id} 行为: {action_type}")
        except Exception as e:
            logger.warning(f"记录用户行为失败: {e}")
            db.rollback()

# @router.post("/render", response_class=HTMLResponse)
# async def render_resume(
#     resume_data: ResumeData,
#     template_name: str = Query("templateA02.html", description="模板名称"),
#     theme_color: Optional[str] = Query(None, description="主题颜色")
# ):
#     """
#     接收简历数据并渲染HTML
#     """
#     try:
#         # 配置主题
#         theme_config = {}
#         if theme_color:
#             theme_config["theme_color"] = theme_color

#         # 渲染简历模板
#         html_content = resume_renderer.render_template(resume_data, template_name, theme_config)
#         return HTMLResponse(content=html_content, status_code=200)
#     except Exception as e:
#         logger.exception("渲染简历时出错")
#         raise HTTPException(status_code=500, detail=f"渲染简历时出错: {str(e)}")

@router.post("/preview", response_class=JSONResponse)
async def preview_resume(
    request: Request,
    resume_data: dict = Body(..., embed=True),
    template_id: str = Body(..., embed=True, description="模板ID"),
    theme_config: dict = Body(..., embed=True, description="主题配置"),
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """
    预览简历数据，返回处理后的JSON和HTML片段
    """
    logger.info("=== 开始处理简历预览请求 ===")
    logger.info(f"接收到的模板ID: {template_id}")
    logger.info(f"接收到的主题配置: {theme_config}")
    logger.info(f"用户ID: {current_user.id if current_user else '未登录'}")

    try:
        resume_data = ResumeData(**resume_data)

        # 渲染简历模板
        logger.info(f"开始渲染预览模板: {template_id}")
        html_content = resume_renderer.render_template(resume_data, template_id, theme_config)
        logger.info(f"预览模板渲染完成，HTML长度: {len(html_content)}")

        # 记录用户行为
        record_user_action(
            db=db,
            user=current_user,
            action_type="preview_resume",
            template_id=template_id,
            action_content={
                "template_id": template_id,
                "theme_config": theme_config
            }
        )

        # 返回处理后的简历数据和HTML
        return JSONResponse(
            content={
                "message": "简历预览生成成功",
                "html": html_content,
                "template_id": template_id,
                "theme_config": theme_config,
                "resume_data": resume_data.model_dump()
            }
        )
    except Exception as e:
        logger.exception("预览简历时出错")
        raise HTTPException(status_code=500, detail=f"预览简历时出错: {str(e)}")

@router.post("/export-pdf", response_class=Response)
async def export_pdf(
    resume_data: dict = Body(..., embed=True),
    template_id: str = Body(..., embed=True, description="模板ID"),
    theme_config: dict = Body(..., embed=True, description="主题配置"),
    current_user: Optional[User] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    导出简历为PDF文件
    """
    logger.info("=== 开始处理PDF导出请求 ===")
    logger.info(f"接收到的模板ID: {template_id}")
    logger.info(f"接收到的主题配置: {theme_config}")
    logger.info(f"用户ID: {current_user.id if current_user else '未登录'}")

    print(f'resume_data: {resume_data}')
    print(f'template_id: {template_id}')
    print(f'theme_config: {theme_config}')

    resume_data = ResumeData(**resume_data)
    try:
        # # 配置主题
        # CONFIG = {}
        # if theme_color:
        #     CONFIG["theme_color"] = theme_color

        # 渲染简历模板
        logger.info(f"开始渲染模板: {template_id}")
        html_content = resume_renderer.render_template(resume_data, template_id, theme_config)
        logger.info(f"模板渲染完成，HTML长度: {len(html_content)}")

        # save to debug
        debug_filename = f'output/resume_{template_id}.html'
        with open(debug_filename, 'w') as f:
            f.write(html_content)
        logger.info(f"调试文件已保存: {debug_filename}")

        # 检查PDF服务健康状态
        health_status = await pdf_service.check_health()
        if health_status.get("status") != "ok":
            raise HTTPException(
                status_code=503,
                detail=f"PDF服务不可用: {health_status.get('message', '未知错误')}"
            )

        # 设置PDF选项
        filename = 'resume.pdf'
        pdf_options = {
            "filename": filename,
            # "format": "A4",
            # "margin": {
            #     "top": "20mm",
            #     "right": "20mm",
            #     "bottom": "20mm",
            #     "left": "20mm"
            # },
            # "printBackground": True
        }

        logger.info("发送HTML内容到PDF服务转换")
        # 转换为PDF
        pdf_result = await pdf_service.html_to_pdf(html_content, pdf_options)

        # 检查结果
        if isinstance(pdf_result, dict) and "error" in pdf_result:
            raise HTTPException(
                status_code=500,
                detail=f"PDF生成失败: {pdf_result.get('message', pdf_result.get('error', '未知错误'))}"
            )

        # 记录用户行为
        record_user_action(
            db=db,
            user=current_user,
            action_type="download_pdf",
            template_id=template_id,
            action_content={
                "template_id": template_id,
                "theme_config": theme_config
            }
        )

        logger.info("PDF生成成功，返回文件")
        # 返回PDF文件
        return Response(
            content=pdf_result,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("导出PDF时出错")
        raise HTTPException(status_code=500, detail=f"导出PDF时出错: {str(e)}")

@router.post("/export-jpeg", response_class=Response)
async def export_jpeg(
    resume_data: dict = Body(..., embed=True),
    template_id: str = Body(..., embed=True, description="模板ID"),
    theme_config: dict = Body(..., embed=True, description="主题配置"),
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """
    导出简历为JPEG图片
    """
    logger.info("=== 开始处理JPEG导出请求 ===")
    logger.info(f"接收到的模板ID: {template_id}")
    logger.info(f"接收到的主题配置: {theme_config}")
    logger.info(f"用户ID: {current_user.id if current_user else '未登录'}")

    # for key, element in resume_data:
    #     if key == 'basicInfo':
    #         for sub_key, sub_element in element:
    #             if sub_key == 'photoUrl':
    #                 continue
    #     else:
    #         print(f'{key}, {element}')

    # print(f'resume_data: {resume_data}')
    print(f'\ncustome: {resume_data['custom1']}')
    print(f'custom3: {resume_data['custom3']}')
    print(f'template_id: {template_id}')
    print(f'theme_config: {theme_config}')

    resume_data = ResumeData(**resume_data)
    # if 'fontSize' in theme_config:
    #     theme_config['fontSize'] = theme_config['fontSize'].replace('pt', '').replace('rpx', '').replace('px', '')

    try:
        # 渲染简历模板
        logger.info(f"开始渲染模板: {template_id}")
        html_content = resume_renderer.render_template(resume_data, template_id, theme_config)
        logger.info(f"模板渲染完成，HTML长度: {len(html_content)}")

        # save to debug
        debug_filename = f'output/resume_{template_id}_jpeg.html'
        with open(debug_filename, 'w') as f:
            f.write(html_content)
        logger.info(f"调试文件已保存: {debug_filename}")

        # 检查PDF服务健康状态
        health_status = await pdf_service.check_health()
        if health_status.get("status") != "ok":
            raise HTTPException(
                status_code=503,
                detail=f"转换服务不可用: {health_status.get('message', '未知错误')}"
            )

        # 设置JPEG选项
        filename = 'resume.jpeg'
        jpeg_options = {
            "filename": filename,
            "imageOptions": {
                "type": "jpeg",
                "quality": 90,
                "fullPage": True
            }
        }

        logger.info("发送HTML内容到转换服务生成JPEG")
        # 转换为JPEG
        export_result = await pdf_service.html_to_jpeg(html_content, jpeg_options)

        # 检查结果
        if isinstance(export_result, dict) and "error" in export_result:
            raise HTTPException(
                status_code=500,
                detail=f"JPEG生成失败: {export_result.get('message', export_result.get('error', '未知错误'))}"
            )

        # 记录用户行为
        record_user_action(
            db=db,
            user=current_user,
            action_type="export_jpeg",
            template_id=template_id,
            action_content={
                "template_id": template_id,
                "theme_config": theme_config
            }
        )

        logger.info("JPEG生成成功，返回文件")
        # 返回JPEG文件
        return Response(
            content=export_result,
            media_type="image/jpeg",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("导出JPEG时出错")
        raise HTTPException(status_code=500, detail=f"导出JPEG时出错: {str(e)}")



















@router.get("/templates", response_class=JSONResponse)
async def get_templates():
    """
    获取所有可用的模板列表
    """
    try:
        # 获取模板目录
        template_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "app", "templates")

        # 获取所有html文件
        templates = [f for f in os.listdir(template_dir) if f.endswith('.html')]

        return JSONResponse(
            content={
                "message": "获取模板列表成功",
                "templates": templates
            }
        )
    except Exception as e:
        logger.exception("获取模板列表时出错")
        raise HTTPException(status_code=500, detail=f"获取模板列表时出错: {str(e)}")