"""
用户反馈系统API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from app.database import get_db
from app.models import User, Feedback, FeedbackReply, FeedbackStatus
from app.schemas.user import (
    FeedbackCreate,
    FeedbackUpdate,
    FeedbackReplyCreate,
    FeedbackInfo,
    FeedbackReplyInfo,
    MessageResponse,
    ListResponse,
    FeedbackStatusEnum
)
from app.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/feedback",
    tags=["反馈系统"],
    responses={404: {"description": "未找到"}},
)

@router.post("", response_model=MessageResponse)
async def create_feedback(
    feedback_data: FeedbackCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    提交用户反馈
    """
    try:
        # 创建反馈记录
        feedback = Feedback(
            user_id=current_user.id,
            content=feedback_data.content,
            contact_info=feedback_data.contact_info,
            status=FeedbackStatus.PENDING
        )
        
        db.add(feedback)
        db.commit()
        db.refresh(feedback)
        
        logger.info(f"用户 {current_user.id} 提交反馈: {feedback.id}")
        
        return MessageResponse(message="反馈提交成功，我们会尽快处理")
        
    except Exception as e:
        logger.exception("提交反馈异常")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交反馈失败"
        )

@router.get("", response_model=ListResponse)
async def get_user_feedback(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    status_filter: Optional[FeedbackStatusEnum] = Query(None, description="状态筛选"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取用户反馈列表
    """
    try:
        # 构建查询
        query = db.query(Feedback).filter(Feedback.user_id == current_user.id)
        
        # 添加状态筛选
        if status_filter:
            query = query.filter(Feedback.status == status_filter.value)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        feedbacks = query.order_by(Feedback.created_at.desc()).offset(offset).limit(limit).all()
        
        # 获取反馈回复
        feedback_list = []
        for feedback in feedbacks:
            replies = db.query(FeedbackReply).filter(
                FeedbackReply.feedback_id == feedback.id
            ).order_by(FeedbackReply.created_at.asc()).all()
            
            feedback_info = FeedbackInfo.model_validate(feedback)
            feedback_info.replies = [FeedbackReplyInfo.model_validate(reply) for reply in replies]
            feedback_list.append(feedback_info)
        
        return ListResponse(
            total=total,
            items=feedback_list
        )
        
    except Exception as e:
        logger.exception("获取用户反馈列表异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取反馈列表失败"
        )

@router.get("/{feedback_id}", response_model=FeedbackInfo)
async def get_feedback_detail(
    feedback_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取反馈详情
    """
    try:
        # 查找反馈记录
        feedback = db.query(Feedback).filter(
            Feedback.id == feedback_id,
            Feedback.user_id == current_user.id
        ).first()
        
        if not feedback:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="反馈记录不存在"
            )
        
        # 获取回复列表
        replies = db.query(FeedbackReply).filter(
            FeedbackReply.feedback_id == feedback_id
        ).order_by(FeedbackReply.created_at.asc()).all()
        
        feedback_info = FeedbackInfo.model_validate(feedback)
        feedback_info.replies = [FeedbackReplyInfo.model_validate(reply) for reply in replies]
        
        return feedback_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("获取反馈详情异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取反馈详情失败"
        )

# 管理员接口（需要额外的权限验证）
@router.post("/{feedback_id}/reply", response_model=MessageResponse)
async def reply_feedback(
    feedback_id: int,
    reply_data: FeedbackReplyCreate,
    db: Session = Depends(get_db)
    # TODO: 添加管理员权限验证
):
    """
    管理员回复反馈（需要管理员权限）
    """
    try:
        # 查找反馈记录
        feedback = db.query(Feedback).filter(Feedback.id == feedback_id).first()
        
        if not feedback:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="反馈记录不存在"
            )
        
        # 创建回复记录
        reply = FeedbackReply(
            feedback_id=feedback_id,
            admin_name=reply_data.admin_name,
            reply_content=reply_data.reply_content
        )
        
        db.add(reply)
        
        # 更新反馈状态
        feedback.status = FeedbackStatus.REPLIED
        
        db.commit()
        
        logger.info(f"管理员 {reply_data.admin_name} 回复反馈 {feedback_id}")
        
        return MessageResponse(message="回复成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("回复反馈异常")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="回复反馈失败"
        )

@router.put("/{feedback_id}/status", response_model=MessageResponse)
async def update_feedback_status(
    feedback_id: int,
    status_update: FeedbackUpdate,
    db: Session = Depends(get_db)
    # TODO: 添加管理员权限验证
):
    """
    更新反馈状态（需要管理员权限）
    """
    try:
        # 查找反馈记录
        feedback = db.query(Feedback).filter(Feedback.id == feedback_id).first()
        
        if not feedback:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="反馈记录不存在"
            )
        
        # 更新状态
        feedback.status = status_update.status.value
        db.commit()
        
        logger.info(f"反馈 {feedback_id} 状态更新为 {status_update.status.value}")
        
        return MessageResponse(message="状态更新成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("更新反馈状态异常")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新反馈状态失败"
        )
