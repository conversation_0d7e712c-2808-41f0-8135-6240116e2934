"""
微信API服务
"""
import requests
import logging
from typing import Optional, Dict, Any
from app.config import settings

logger = logging.getLogger(__name__)

class WeChatService:
    """微信服务类"""
    
    def __init__(self):
        self.app_id = settings.WECHAT_APP_ID
        self.app_secret = settings.WECHAT_APP_SECRET
        self.api_url = settings.WECHAT_API_URL
    
    async def code_to_session(self, code: str) -> Optional[Dict[str, Any]]:
        """
        通过微信登录code获取用户session信息
        
        Args:
            code: 微信登录code
            
        Returns:
            包含openid、session_key等信息的字典，失败返回None
        """
        if not self.app_id or not self.app_secret:
            logger.error("微信小程序配置未设置")
            return None
        
        params = {
            "appid": self.app_id,
            "secret": self.app_secret,
            "js_code": code,
            "grant_type": "authorization_code"
        }
        
        try:
            response = requests.get(self.api_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # 检查是否有错误
            if "errcode" in data:
                logger.error(f"微信API错误: {data.get('errcode')} - {data.get('errmsg')}")
                return None
            
            # 验证必要字段
            if "openid" not in data:
                logger.error("微信API响应缺少openid")
                return None
            
            logger.info(f"微信登录成功，openid: {data['openid']}")
            return data
            
        except requests.RequestException as e:
            logger.error(f"微信API请求失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"微信登录处理异常: {str(e)}")
            return None
    
    def validate_user_info(self, user_info: Dict[str, Any]) -> bool:
        """
        验证用户信息格式
        
        Args:
            user_info: 用户信息字典
            
        Returns:
            验证是否通过
        """
        required_fields = ["nickName"]
        
        for field in required_fields:
            if field not in user_info:
                logger.warning(f"用户信息缺少必要字段: {field}")
                return False
        
        return True

# 创建微信服务实例
wechat_service = WeChatService()
