"""
用户相关数据模型
"""
from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, Enum, JSON, Boolean
from sqlalchemy.sql import func
from app.database import Base
import enum


class User(Base):
    """用户表"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    openid = Column(String(100), unique=True, nullable=False, index=True, comment="微信openid")
    unionid = Column(String(100), nullable=True, comment="微信unionid")
    nickname = Column(String(100), nullable=True, comment="用户昵称")
    avatar_url = Column(Text, nullable=True, comment="头像URL")
    gender = Column(Integer, nullable=True, comment="性别：0未知，1男，2女")
    country = Column(String(50), nullable=True, comment="国家")
    province = Column(String(50), nullable=True, comment="省份")
    city = Column(String(50), nullable=True, comment="城市")
    is_member = Column(Boolean, default=False, nullable=False, comment="是否会员")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )


class ActionType(enum.Enum):
    """用户行为类型枚举"""
    PREVIEW_RESUME = "preview_resume"
    DOWNLOAD_PDF = "download_pdf"
    USE_TEMPLATE = "use_template"
    EXPORT_JPEG = "export_jpeg"


class UserAction(Base):
    """用户行为记录表"""
    __tablename__ = "user_actions"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, index=True, comment="用户ID")
    action_type = Column(String(50), nullable=False, comment="行为类型")
    action_content = Column(JSON, nullable=True, comment="行为详细内容")
    template_id = Column(String(50), nullable=True, comment="使用的模板ID")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")


class FeedbackStatus(enum.Enum):
    """反馈状态枚举"""
    PENDING = "pending"
    REPLIED = "replied"
    CLOSED = "closed"


class Feedback(Base):
    """反馈表"""
    __tablename__ = "feedback"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, index=True, comment="用户ID")
    content = Column(Text, nullable=False, comment="反馈内容")
    contact_info = Column(String(200), nullable=True, comment="联系方式")
    status = Column(
        Enum(FeedbackStatus),
        default=FeedbackStatus.PENDING,
        comment="状态"
    )
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )


class FeedbackReply(Base):
    """反馈回复表"""
    __tablename__ = "feedback_replies"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    feedback_id = Column(Integer, nullable=False, index=True, comment="反馈ID")
    admin_name = Column(String(100), nullable=False, comment="管理员名称")
    reply_content = Column(Text, nullable=False, comment="回复内容")
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
