"""
应用配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Settings:
    """应用设置"""
    
    # 应用基本配置
    APP_NAME: str = "Resume Service API"
    VERSION: str = "1.0.0"
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    
    # 数据库配置
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL", 
        "mysql+pymysql://resume_user:Resume123!@localhost/resume_service"
    )
    
    # JWT配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # 微信小程序配置
    WECHAT_APP_ID: str = os.getenv("WECHAT_APP_ID", "")
    WECHAT_APP_SECRET: str = os.getenv("WECHAT_APP_SECRET", "")
    WECHAT_API_URL: str = "https://api.weixin.qq.com/sns/jscode2session"
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    
    # PDF服务配置
    PDF_SERVICE_URL: str = os.getenv("PDF_SERVICE_URL", "http://localhost:3001")

# 创建设置实例
settings = Settings()

# 验证必要的配置
def validate_config():
    """验证必要的配置项"""
    if not settings.WECHAT_APP_ID:
        print("警告: WECHAT_APP_ID 未设置")
    if not settings.WECHAT_APP_SECRET:
        print("警告: WECHAT_APP_SECRET 未设置")
    if settings.SECRET_KEY == "your-secret-key-change-in-production":
        print("警告: 请在生产环境中更改 SECRET_KEY")

if __name__ == "__main__":
    validate_config()
